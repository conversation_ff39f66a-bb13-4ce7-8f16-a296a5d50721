package com.example.taobao.data

import androidx.room.*
import kotlinx.coroutines.flow.Flow

/**
 * 订单数据访问对象接口，定义对订单表的操作
 */
@Dao
interface OrderDao {
    /**
     * 获取所有订单，返回Flow以便于观察数据变化
     */
    @Query("SELECT * FROM orders ORDER BY id DESC")
    fun getAllOrders(): Flow<List<OrderEntity>>

    /**
     * 根据ID获取一个订单
     */
    @Query("SELECT * FROM orders WHERE id = :orderId")
    fun getOrderById(orderId: String): Flow<OrderEntity?>

    /**
     * 插入一个新订单，如果已存在则替换
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrder(order: OrderEntity)

    /**
     * 插入多个订单，如果已存在则替换
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrders(orders: List<OrderEntity>)

    /**
     * 更新一个订单
     */
    @Update
    suspend fun updateOrder(order: OrderEntity)

    /**
     * 删除一个订单
     */
    @Delete
    suspend fun deleteOrder(order: OrderEntity)

    /**
     * 根据ID删除一个订单
     */
    @Query("DELETE FROM orders WHERE id = :orderId")
    suspend fun deleteOrderById(orderId: String)

    /**
     * 删除所有订单
     */
    @Query("DELETE FROM orders")
    suspend fun deleteAllOrders()
} 