<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white"
    android:fitsSystemWindows="true">

    <LinearLayout
        android:id="@+id/header_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Top Bar -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:background="@color/white"
            android:paddingStart="8dp"
            android:paddingEnd="8dp">
            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_back_arrow"
                android:padding="6dp"
                android:contentDescription="返回" />
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="36dp"
                android:layout_weight="1"
                android:background="@drawable/search_bg"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp">
                
                <ImageView
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:src="@drawable/ic_search"
                    android:layout_marginStart="10dp"/>
                    
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="搜索订单"
                    android:textSize="14sp"
                    android:textColor="#999999"
                    android:layout_marginStart="8dp"/>
            </LinearLayout>

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_more_horiz"
                android:padding="6dp"
                android:layout_marginStart="8dp"
                android:contentDescription="更多"/>
        </LinearLayout>
    </LinearLayout>

    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/gray_bg"/>

</LinearLayout> 