package com.example.taobao.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.taobao.databinding.FragmentEditOrderBinding
import com.example.taobao.ui.OrderPending

class EditOrderFragment : Fragment() {

    private var _binding: FragmentEditOrderBinding? = null
    private val binding get() = _binding!!
    private lateinit var viewModel: OrderViewModel
    private lateinit var editableAdapter: EditableOrderAdapter

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = FragmentEditOrderBinding.inflate(inflater, container, false)
        viewModel = ViewModelProvider(requireActivity()).get(OrderViewModel::class.java)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        editableAdapter = EditableOrderAdapter(
            onDeleteClick = { order ->
                viewModel.deleteOrder(order)
            },
            onEditClick = { order ->
                viewModel.onOrderSelectedForEdit(order)
            }
        )

        binding.editOrderRecyclerView.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = editableAdapter
        }

        viewModel.orders.observe(viewLifecycleOwner) { orders ->
            editableAdapter.submitList(orders)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}