package com.example.taobao.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.taobao.MainActivity
import com.example.taobao.R
import com.example.taobao.ui.OrderPending

class PendingReceiptFragment : Fragment() {

    private lateinit var viewModel: OrderViewModel
    private lateinit var orderAdapter: OrderPendingAdapter
    private var showOnlyPendingReceipt = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            showOnlyPendingReceipt = it.getBoolean(ARG_SHOW_ONLY_PENDING_RECEIPT, false)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_pending_receipt, container, false)
        viewModel = ViewModelProvider(requireActivity()).get(OrderViewModel::class.java)
        
        // Setup RecyclerView
        val recyclerView: RecyclerView = view.findViewById(R.id.orderRecyclerView)
        recyclerView.layoutManager = LinearLayoutManager(context)
        orderAdapter = OrderPendingAdapter(emptyList())
        
        // 设置列表项点击事件监听器
        orderAdapter.setOnItemClickListener(object : OrderPendingAdapter.OnItemClickListener {
            override fun onItemClick(order: OrderPending) {
                (activity as? MainActivity)?.navigateToOrderDetail(order.id)
            }
        })
        
        // 设置按钮点击事件监听器
        orderAdapter.setOnButtonClickListener(object : OrderPendingAdapter.OnButtonClickListener {
            override fun onConfirmReceiptClick(order: OrderPending) {
                viewModel.deleteOrder(order)
                Toast.makeText(context, "已确认收货", Toast.LENGTH_SHORT).show()
            }

            override fun onCheckLogisticsClick(order: OrderPending) {
                (activity as? MainActivity)?.navigateToOrderDetail(order.id)
            }

            override fun onModifyAddressClick(order: OrderPending) {
                // 根据订单状态执行不同操作
                if (order.orderStatus == "卖家已发货") {
                    // 待收货状态 - 确认收货
                    viewModel.deleteOrder(order)
                    Toast.makeText(context, "已确认收货", Toast.LENGTH_SHORT).show()
                } else {
                    // 待发货状态 - 修改地址
                Toast.makeText(context, "修改地址", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onUrgeShipmentClick(order: OrderPending) {
                // 根据订单状态执行不同操作
                if (order.orderStatus == "卖家已发货") {
                    // 待收货状态 - 查看物流
                    (activity as? MainActivity)?.navigateToOrderDetail(order.id)
                    Toast.makeText(context, "查看物流", Toast.LENGTH_SHORT).show()
                } else {
                    // 待发货状态 - 催发货
                    Toast.makeText(context, "催发货", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onApplyForInvoiceClick(order: OrderPending) {
                // 根据订单状态执行不同操作
                if (order.orderStatus == "卖家已发货") {
                    // 待收货状态 - 延长收货
                    Toast.makeText(context, "延长收货", Toast.LENGTH_SHORT).show()
                } else {
                    // 待发货状态 - 申请开票
                    Toast.makeText(context, "申请开票", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onMoreClick(order: OrderPending) {
                Toast.makeText(context, "更多操作", Toast.LENGTH_SHORT).show()
            }
        })
        
        recyclerView.adapter = orderAdapter

        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.orders.observe(viewLifecycleOwner) { orders ->
            if (showOnlyPendingReceipt) {
                orderAdapter.updateOrders(orders.filter { it.orderStatus == "卖家已发货" }) // Or whatever status indicates pending receipt
            } else {
            orderAdapter.updateOrders(orders)
            }
        }
    }

    companion object {
        private const val ARG_SHOW_ONLY_PENDING_RECEIPT = "show_only_pending_receipt"

        fun newInstance(showOnlyPendingReceipt: Boolean): PendingReceiptFragment {
            val fragment = PendingReceiptFragment()
            val args = Bundle()
            args.putBoolean(ARG_SHOW_ONLY_PENDING_RECEIPT, showOnlyPendingReceipt)
            fragment.arguments = args
            return fragment
        }
    }
} 