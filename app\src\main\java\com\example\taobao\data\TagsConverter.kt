package com.example.taobao.data

import androidx.room.TypeConverter

/**
 * Room数据库类型转换器，用于将List<String>类型的标签转换为可以存储在数据库中的String类型，
 * 以及将String类型转换回List<String>
 */
class TagsConverter {
    @TypeConverter
    fun fromStringList(tags: List<String>): String {
        return tags.joinToString(",")
    }

    @TypeConverter
    fun toStringList(tagsString: String): List<String> {
        return if (tagsString.isEmpty()) {
            emptyList()
        } else {
            tagsString.split(",").map { it.trim() }
        }
    }
} 