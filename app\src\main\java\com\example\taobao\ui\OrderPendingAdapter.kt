package com.example.taobao.ui

import android.content.Context
import android.graphics.Typeface
import android.text.Spannable
import android.text.SpannableString
import android.text.style.RelativeSizeSpan
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.example.taobao.R

class OrderPendingAdapter(private var orders: List<OrderPending>) : RecyclerView.Adapter<OrderPendingAdapter.ViewHolder>() {

    interface OnItemClickListener {
        fun onItemClick(order: OrderPending)
    }
    
    interface OnButtonClickListener {
        fun onModifyAddressClick(order: OrderPending)
        fun onUrgeShipmentClick(order: OrderPending)
        fun onApplyForInvoiceClick(order: OrderPending)

        // Keep old ones in case they are used elsewhere, but they are not in the new layout
        fun onConfirmReceiptClick(order: OrderPending)
        fun onCheckLogisticsClick(order: OrderPending)
        fun onMoreClick(order: OrderPending)
    }

    private var buttonClickListener: OnButtonClickListener? = null
    private var itemClickListener: OnItemClickListener? = null

    fun setOnButtonClickListener(listener: OnButtonClickListener) {
        this.buttonClickListener = listener
    }

    fun setOnItemClickListener(listener: OnItemClickListener) {
        this.itemClickListener = listener
    }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val tvShopName: TextView = view.findViewById(R.id.tvShopName)
        val tvOrderStatus: TextView = view.findViewById(R.id.tvOrderStatus)
        val ivProduct: ImageView = view.findViewById(R.id.ivProduct)
        val tvProductName: TextView = view.findViewById(R.id.tvProductName)
        val tvProductDesc: TextView = view.findViewById(R.id.tvProductDesc)
        val tvQuantity: TextView = view.findViewById(R.id.tvQuantity)
        val tvProductPrice: TextView = view.findViewById(R.id.tvProductPrice)
        val tvShippingStatus: TextView = view.findViewById(R.id.tvShippingStatus)
        val tvShippingPromise: TextView = view.findViewById(R.id.tvShippingPromise)
        val tvPayAmount: TextView = view.findViewById(R.id.tvPayAmount)
        val btnModifyAddress: Button = view.findViewById(R.id.btnModifyAddress)
        val btnUrgeShipment: Button = view.findViewById(R.id.btnUrgeShipment)
        val btnApplyForInvoice: Button = view.findViewById(R.id.btnApplyForInvoice)
        val tagsContainer: LinearLayout = view.findViewById(R.id.tags_container)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_order_pending, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val order = orders[position]
        holder.tvShopName.text = order.shopName
        holder.tvOrderStatus.text = order.orderStatus
        if (!order.productImgUri.isNullOrEmpty()) {
            holder.ivProduct.setImageURI(android.net.Uri.parse(order.productImgUri))
        } else {
            holder.ivProduct.setImageResource(order.productImg)
        }
        
        // Split name and price for separate views
        holder.tvProductName.text = order.productName
        // Assuming productPrice is "¥95.00", we remove "¥"
        holder.tvProductPrice.text = order.productPrice.replace("¥", "")

        holder.tvProductDesc.text = order.productDesc
        holder.tvQuantity.text = order.quantity
        
        // Bind real shipping data
        holder.tvShippingStatus.text = order.shippingStatus
        holder.tvShippingPromise.text = order.shippingPromise
        
        // Format pay info
        holder.tvPayAmount.text = order.payInfo.replace("¥", "")
        
        // 根据订单状态设置按钮文本
        if (order.orderStatus == "等待卖家发货") {
            // 待发货状态
            holder.btnApplyForInvoice.visibility = View.GONE  // 
            holder.btnUrgeShipment.text = "申请开票"
            holder.btnModifyAddress.text = "催发货"
        } else if (order.orderStatus == "卖家已发货") {
            // 待收货状态
            holder.btnApplyForInvoice.text = "延长收货"
            holder.btnUrgeShipment.text = "查看物流"
            holder.btnModifyAddress.text = "确认收货"
        }

        // Handle tags dynamically
        holder.tagsContainer.removeAllViews()
        val context = holder.itemView.context
        order.tags.forEachIndexed { index, tagText ->
            val tagView = LayoutInflater.from(context).inflate(R.layout.item_tag, holder.tagsContainer, false) as TextView
            tagView.text = tagText
            if (index > 0) {
                val params = tagView.layoutParams as ViewGroup.MarginLayoutParams
                params.marginStart = context.resources.getDimensionPixelSize(R.dimen.tag_margin_start)
                tagView.layoutParams = params
            }
            holder.tagsContainer.addView(tagView)
        }

        // Set button click listeners for new buttons
        holder.btnModifyAddress.setOnClickListener {
            buttonClickListener?.onModifyAddressClick(order)
        }

        holder.btnUrgeShipment.setOnClickListener {
            buttonClickListener?.onUrgeShipmentClick(order)
        }

        holder.btnApplyForInvoice.setOnClickListener {
            buttonClickListener?.onApplyForInvoiceClick(order)
        }

        holder.itemView.setOnClickListener {
            itemClickListener?.onItemClick(order)
        }
    }
    
    fun updateOrders(newOrders: List<OrderPending>) {
        orders = newOrders
        notifyDataSetChanged()
    }

    override fun getItemCount() = orders.size
} 