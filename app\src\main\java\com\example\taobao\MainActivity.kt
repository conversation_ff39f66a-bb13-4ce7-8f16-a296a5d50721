package com.example.taobao

import android.os.Bundle
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.example.taobao.R
import com.example.taobao.databinding.ActivityMainBinding
import com.example.taobao.ui.AddOrderFragment
import com.example.taobao.ui.AllOrdersFragment
import com.example.taobao.ui.EditOrderFragment
import com.example.taobao.ui.OrderDetailFragment
import com.example.taobao.ui.OrderViewModel
import com.example.taobao.ui.PendingReceiptFragment
import com.example.taobao.ui.PlaceholderFragment

class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private lateinit var viewModel: OrderViewModel

    // Fragment instances
    private val allOrdersFragment = AllOrdersFragment()
    // Other fragments are not needed anymore as we simplify the navigation
    // private val pendingPaymentFragment = PlaceholderFragment.newInstance("待付款")
    // ...

    private var activeFragment: Fragment = allOrdersFragment

    fun navigateToOrderDetail(orderId: String) {
        binding.headerContainer.visibility = android.view.View.GONE
        val fragment = OrderDetailFragment.newInstance(orderId)
        supportFragmentManager.beginTransaction()
            .hide(activeFragment)
            .add(R.id.fragment_container, fragment)
            .addToBackStack(null)
            .commit()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        viewModel = ViewModelProvider(this).get(OrderViewModel::class.java)

        if (savedInstanceState == null) {
            supportFragmentManager.beginTransaction()
                .add(R.id.fragment_container, allOrdersFragment)
                .commit()
        }
        activeFragment = allOrdersFragment
        
        setupBackStackListener()
    }

    private fun setupBackStackListener() {
        supportFragmentManager.addOnBackStackChangedListener {
            if (supportFragmentManager.backStackEntryCount == 0) {
                binding.headerContainer.visibility = android.view.View.VISIBLE
            }
        }
    }
}