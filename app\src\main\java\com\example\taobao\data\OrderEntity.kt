package com.example.taobao.data

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.example.taobao.ui.OrderPending

@Entity(tableName = "orders")
data class OrderEntity(
    @PrimaryKey
    val id: String,
    val shopName: String,
    val shopIconUri: String?, // Add shop icon URI
    val orderStatus: String,
    val productImg: Int,
    val productImgUri: String?, // 新增字段，商品图片uri
    val productName: String,
    val productPrice: String,
    val productDesc: String,
    val quantity: String,
    val shippingStatus: String,
    val shippingPromise: String,
    @TypeConverters(TagsConverter::class)
    val tags: List<String>,
    val logistics: String,
    val payInfo: String,

    // New fields for Order Detail
    val buyerAddress: String? = "碧桂园·海昌天澜148幢",
    val buyerName: String? = "淘宝用户 86-152****6073",
    val orderStatusText: String? = "买家已付款",
    val storeRating: String? = "88VIP好评率99%, 平均3天内发货",
    val orderNumber: String? = "4610644706593328843",
    val transactionSnapshotText: String? = "发生交易争议时,可作为判断依据",
    val tmallPoints: String? = "获得25点积分",
    val wechatTransactionId: String? = "4349502572202507022480548733",
    val creationTime: String? = "2025-07-02 16:33:07",
    val paymentTime: String? = "2025-07-02 16:33:14",
    val orderServices: String? = "包含假一赔四等服务",
    val presaleInfo: String = ""
) {
    // 转换为UI层使用的OrderPending对象
    fun toOrderPending(): OrderPending {
        return OrderPending(
            id = id,
            shopName = shopName,
            shopIconUri = shopIconUri,
            orderStatus = orderStatus,
            productImg = productImg,
            productImgUri = productImgUri,
            productName = productName,
            productPrice = productPrice,
            productDesc = productDesc,
            quantity = quantity,
            shippingStatus = shippingStatus,
            shippingPromise = shippingPromise,
            tags = tags,
            logistics = logistics,
            payInfo = payInfo,
            buyerAddress = buyerAddress,
            buyerName = buyerName,
            orderStatusText = orderStatusText,
            storeRating = storeRating,
            orderNumber = orderNumber,
            transactionSnapshotText = transactionSnapshotText,
            tmallPoints = tmallPoints,
            wechatTransactionId = wechatTransactionId,
            creationTime = creationTime,
            paymentTime = paymentTime,
            orderServices = orderServices,
            presaleInfo = presaleInfo
        )
    }
    
    companion object {
        // 从UI层的OrderPending对象创建数据库实体
        fun fromOrderPending(order: OrderPending): OrderEntity {
            return OrderEntity(
                id = order.id,
                shopName = order.shopName,
                shopIconUri = order.shopIconUri,
                orderStatus = order.orderStatus,
                productImg = order.productImg,
                productImgUri = order.productImgUri,
                productName = order.productName,
                productPrice = order.productPrice,
                productDesc = order.productDesc,
                quantity = order.quantity,
                shippingStatus = order.shippingStatus,
                shippingPromise = order.shippingPromise,
                tags = order.tags,
                logistics = order.logistics,
                payInfo = order.payInfo,
                buyerAddress = order.buyerAddress,
                buyerName = order.buyerName,
                orderStatusText = order.orderStatusText,
                storeRating = order.storeRating,
                orderNumber = order.orderNumber,
                transactionSnapshotText = order.transactionSnapshotText,
                tmallPoints = order.tmallPoints,
                wechatTransactionId = order.wechatTransactionId,
                creationTime = order.creationTime,
                paymentTime = order.paymentTime,
                orderServices = order.orderServices,
                presaleInfo = order.presaleInfo
            )
        }
    }
} 