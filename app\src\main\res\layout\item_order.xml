<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="#FFFFFF"
    android:layout_marginBottom="8dp"
    android:padding="0dp">

    <!-- 店铺名和状态 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="12dp">
        <TextView
            android:id="@+id/tvShopName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Apple Store 官方旗舰店"
            android:textColor="#222222"
            android:textSize="16sp"
            android:maxLines="1"
            android:ellipsize="end"/>
        <TextView
            android:id="@+id/tvOrderStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="买家已发货"
            android:textColor="#FF4400"
            android:textSize="14sp"/>
    </LinearLayout>

    <!-- 商品信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingStart="12dp"
        android:paddingEnd="12dp">
        <ImageView
            android:id="@+id/ivProduct"
            android:layout_width="72dp"
            android:layout_height="72dp"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_launcher_foreground"/>
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginStart="8dp">
            <TextView
                android:id="@+id/tvProductName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Apple/苹果 iPhone 16 Pro ..."
                android:textColor="#222222"
                android:textSize="15sp"
                android:maxLines="2"
                android:ellipsize="end"/>
            <TextView
                android:id="@+id/tvProductDesc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="白色钛金属;1TB"
                android:textColor="#888888"
                android:textSize="13sp"
                android:layout_marginTop="2dp"/>
            <TextView
                android:id="@+id/tvProductTag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="七天无理由退换"
                android:textColor="#FF9900"
                android:textSize="12sp"
                android:layout_marginTop="2dp"/>
        </LinearLayout>
    </LinearLayout>

    <!-- 物流信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingStart="12dp"
        android:paddingEnd="12dp"
        android:paddingTop="8dp">
        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@android:drawable/ic_menu_directions"
            android:tint="#888888"/>
        <TextView
            android:id="@+id/tvLogistics"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="运输中 你的宝贝将由顺丰速运护送"
            android:textColor="#222222"
            android:textSize="14sp"
            android:layout_marginStart="6dp"/>
    </LinearLayout>

    <!-- 实付款 -->
    <TextView
        android:id="@+id/tvPay"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="实付款￥21,168.96"
        android:textColor="#222222"
        android:textSize="15sp"
        android:layout_marginTop="8dp"
        android:layout_marginStart="12dp"/>

    <!-- 操作按钮区 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end"
        android:padding="12dp">
        <Button
            android:id="@+id/btnMore"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:text="更多"
            android:textSize="13sp"
            android:background="@drawable/btn_gray_bg"
            android:textColor="#333333"
            android:layout_marginEnd="8dp"/>
        <Button
            android:id="@+id/btnEditAddress"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:text="修改地址"
            android:textSize="13sp"
            android:background="@drawable/btn_gray_bg"
            android:textColor="#333333"
            android:layout_marginEnd="8dp"/>
        <Button
            android:id="@+id/btnViewLogistics"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:text="查看物流"
            android:textSize="13sp"
            android:background="@drawable/btn_gray_bg"
            android:textColor="#333333"
            android:layout_marginEnd="8dp"/>
        <Button
            android:id="@+id/btnRemindSend"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:text="催发货"
            android:textSize="13sp"
            android:background="@drawable/btn_orange_bg"
            android:textColor="#FFFFFF"/>
    </LinearLayout>

</LinearLayout> 