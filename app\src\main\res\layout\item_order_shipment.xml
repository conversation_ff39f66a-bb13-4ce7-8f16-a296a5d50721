<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/order_item_bg"
    android:orientation="vertical"
    android:paddingTop="12dp"
    android:paddingBottom="12dp">

    <!-- 店铺信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="天猫"
            android:textColor="@color/tmall_bright_red"
            android:textSize="15sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvShopName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="2dp"
            android:text="Apple Store 官方旗舰店"
            android:textColor="@color/black_text"
            android:textSize="15sp"
            android:textStyle="bold" />

        <ImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginStart="4dp"
            android:src="@drawable/ic_chevron_right_gray" />

        <Space
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1"/>

        <TextView
            android:id="@+id/tvOrderStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="等待卖家发货"
            android:textColor="@color/taobao_orange"
            android:textSize="14sp" />
    </LinearLayout>

    <!-- 商品信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <ImageView
            android:id="@+id/ivProduct"
            android:layout_width="90dp"
            android:layout_height="90dp"
            android:scaleType="centerCrop"
            android:src="@drawable/test" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp">

            <LinearLayout
                android:id="@+id/ll_price"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_alignTop="@id/tvProductName"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="4dp"
                    android:text="¥"
                    android:textColor="@color/black_text"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvProductPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="4dp"
                    android:text="6999.00"
                    android:textColor="@color/black_text"
                    android:textSize="16sp"
                    android:textStyle="bold" />
            </LinearLayout>

            <TextView
                android:id="@+id/tvProductName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toStartOf="@+id/ll_price"
                android:layout_marginEnd="8dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:layout_marginTop="4dp"
                android:text="Apple/苹果iPhone 12 Pro ..."
                android:textColor="@color/black_text"
                android:textSize="13sp" />

            <TextView
                android:id="@+id/tvQuantity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_alignTop="@id/tvProductDesc"
                android:text="x1"
                android:textColor="@color/gray_text"
                android:textSize="13sp" />

            <TextView
                android:id="@+id/tvProductDesc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/tvProductName"
                android:layout_marginTop="8dp"
                android:layout_toStartOf="@id/tvQuantity"
                android:layout_marginEnd="8dp"
                android:text="海军蓝, 256G, 6G内存"
                android:textColor="@color/gray_text"
                android:textSize="13sp" />

            <LinearLayout
                android:id="@+id/tags_container"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/tvProductDesc"
                android:layout_marginTop="12dp"
                android:orientation="horizontal">

                <TextView
                    style="@style/TaobaoTag"
                    android:text="假一赔四" />

                <TextView
                    style="@style/TaobaoTag"
                    android:layout_marginStart="8dp"
                    android:text="极速退款" />

                <TextView
                    style="@style/TaobaoTag"
                    android:layout_marginStart="8dp"
                    android:text="7天无理由退换" />
            </LinearLayout>
        </RelativeLayout>
    </LinearLayout>

    <!-- 发货信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        android:layout_marginTop="0dp"
        android:layout_marginBottom="0dp"
        android:background="@drawable/shipping_info_bar_bg"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp"
        android:paddingVertical="8dp">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/fahuo" />

        <TextView
            android:id="@+id/tvShippingStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="待发货"
            android:textColor="@color/black_text"
            android:textSize="13sp" />

        <TextView
            android:id="@+id/tvShippingPromise"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="预售, 10月30日16:27前发货"
            android:textColor="@color/black_text"
            android:textSize="13sp" />
    </LinearLayout>

    <!-- 支付信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end|center_vertical"
        android:orientation="horizontal"
        android:paddingTop="8dp"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="含退货包服务"
            android:textColor="@color/gray_text"
            android:textSize="14sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:text="实付款"
            android:textColor="@color/black_text"
            android:textSize="14sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:text="¥"
            android:textColor="@color/black_text"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tvPayAmount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="95.00"
            android:textColor="@color/black_text"
            android:textSize="16sp"
            android:textStyle="bold" />
    </LinearLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:orientation="horizontal"
        android:paddingTop="8dp"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <Button
            android:id="@+id/btnApplyForInvoice"
            style="@style/TaobaoFilterButton"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:elevation="0dp"
            android:stateListAnimator="@null"
            android:text="申请开票" />

        <Button
            android:id="@+id/btnUrgeShipment"
            style="@style/TaobaoFilterButton.Selected"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:layout_marginStart="8dp"
            android:elevation="0dp"
            android:stateListAnimator="@null"
            android:text="催发货" />

        <!-- <Button
            android:id="@+id/btnModifyAddress"
            style="@style/TaobaoFilterButton.Selected"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:layout_marginStart="8dp"
            android:elevation="0dp"
            android:stateListAnimator="@null"
            android:text="修改地址" /> -->
    </LinearLayout>
</LinearLayout> 