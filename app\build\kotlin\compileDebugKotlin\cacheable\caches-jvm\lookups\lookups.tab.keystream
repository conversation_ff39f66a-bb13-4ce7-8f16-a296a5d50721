  Activity android.app  Application android.app  ActivityMainBinding android.app.Activity  AllOrdersFragment android.app.Activity  OrderDetailFragment android.app.Activity  OrderViewModel android.app.Activity  R android.app.Activity  ViewModelProvider android.app.Activity  android android.app.Activity  getSystemService android.app.Activity  java android.app.Activity  layoutInflater android.app.Activity  newInstance android.app.Activity  onCreate android.app.Activity  ClipData android.content  ClipboardManager android.content  Context android.content  newPlainText android.content.ClipData  setPrimaryClip  android.content.ClipboardManager  ActivityMainBinding android.content.Context  AllOrdersFragment android.content.Context  CLIPBOARD_SERVICE android.content.Context  OrderDetailFragment android.content.Context  OrderViewModel android.content.Context  R android.content.Context  ViewModelProvider android.content.Context  android android.content.Context  applicationContext android.content.Context  getSystemService android.content.Context  java android.content.Context  newInstance android.content.Context  	resources android.content.Context  ActivityMainBinding android.content.ContextWrapper  AllOrdersFragment android.content.ContextWrapper  OrderDetailFragment android.content.ContextWrapper  OrderViewModel android.content.ContextWrapper  R android.content.ContextWrapper  ViewModelProvider android.content.ContextWrapper  android android.content.ContextWrapper  java android.content.ContextWrapper  newInstance android.content.ContextWrapper  getDimensionPixelSize android.content.res.Resources  Typeface android.graphics  Uri android.net  let android.net.Uri  parse android.net.Uri  toString android.net.Uri  Build 
android.os  Bundle 
android.os  	getString android.os.BaseBundle  	putString android.os.BaseBundle  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  	getString android.os.Bundle  let android.os.Bundle  	putString android.os.Bundle  	Spannable android.text  SpannableString android.text  RelativeSizeSpan android.text.style  	StyleSpan android.text.style  Gravity android.view  LayoutInflater android.view  View android.view  	ViewGroup android.view  ActivityMainBinding  android.view.ContextThemeWrapper  AllOrdersFragment  android.view.ContextThemeWrapper  OrderDetailFragment  android.view.ContextThemeWrapper  OrderViewModel  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  ViewModelProvider  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  newInstance  android.view.ContextThemeWrapper  from android.view.LayoutInflater  inflate android.view.LayoutInflater  GONE android.view.View  OnClickListener android.view.View  VISIBLE android.view.View  context android.view.View  findViewById android.view.View  layoutParams android.view.View  setOnClickListener android.view.View  
visibility android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  MarginLayoutParams android.view.ViewGroup  addView android.view.ViewGroup  context android.view.ViewGroup  removeAllViews android.view.ViewGroup  marginStart )android.view.ViewGroup.MarginLayoutParams  Button android.widget  EditText android.widget  	ImageView android.widget  LinearLayout android.widget  
ScrollView android.widget  TextView android.widget  Toast android.widget  setOnClickListener android.widget.Button  setText android.widget.EditText  text android.widget.EditText  setImageResource android.widget.ImageView  setImageURI android.widget.ImageView  addView android.widget.LinearLayout  removeAllViews android.widget.LinearLayout  
visibility android.widget.LinearLayout  layoutParams android.widget.TextView  setText android.widget.TextView  text android.widget.TextView  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  ActivityMainBinding #androidx.activity.ComponentActivity  AllOrdersFragment #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  Fragment #androidx.activity.ComponentActivity  OrderDetailFragment #androidx.activity.ComponentActivity  OrderViewModel #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  ViewModelProvider #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  newInstance #androidx.activity.ComponentActivity  ActivityMainBinding -androidx.activity.ComponentActivity.Companion  AllOrdersFragment -androidx.activity.ComponentActivity.Companion  OrderDetailFragment -androidx.activity.ComponentActivity.Companion  OrderViewModel -androidx.activity.ComponentActivity.Companion  R -androidx.activity.ComponentActivity.Companion  ViewModelProvider -androidx.activity.ComponentActivity.Companion  android -androidx.activity.ComponentActivity.Companion  java -androidx.activity.ComponentActivity.Companion  newInstance -androidx.activity.ComponentActivity.Companion  ActivityResultCallback androidx.activity.result  ActivityResultLauncher androidx.activity.result  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  
GetContent 9androidx.activity.result.contract.ActivityResultContracts  AppCompatActivity androidx.appcompat.app  ActivityMainBinding (androidx.appcompat.app.AppCompatActivity  AllOrdersFragment (androidx.appcompat.app.AppCompatActivity  OrderDetailFragment (androidx.appcompat.app.AppCompatActivity  OrderViewModel (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  ViewModelProvider (androidx.appcompat.app.AppCompatActivity  android (androidx.appcompat.app.AppCompatActivity  java (androidx.appcompat.app.AppCompatActivity  newInstance (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  isSystemInDarkTheme androidx.compose.foundation  ColorScheme androidx.compose.material3  
MaterialTheme androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  
Composable androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  Color androidx.compose.ui.graphics  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  TextUnit androidx.compose.ui.unit  sp androidx.compose.ui.unit  ActivityMainBinding #androidx.core.app.ComponentActivity  AllOrdersFragment #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Fragment #androidx.core.app.ComponentActivity  OrderDetailFragment #androidx.core.app.ComponentActivity  OrderViewModel #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  ViewModelProvider #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  newInstance #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  toUri androidx.core.net  Fragment androidx.fragment.app  FragmentActivity androidx.fragment.app  FragmentManager androidx.fragment.app  FragmentTransaction androidx.fragment.app  activityViewModels androidx.fragment.app  ARG_MESSAGE androidx.fragment.app.Fragment  ARG_ORDER_ID androidx.fragment.app.Fragment  ActivityResultContracts androidx.fragment.app.Fragment  AddOrderFragment androidx.fragment.app.Fragment  Bundle androidx.fragment.app.Fragment  ClipData androidx.fragment.app.Fragment  ClipboardManager androidx.fragment.app.Fragment  Context androidx.fragment.app.Fragment  EditOrderFragment androidx.fragment.app.Fragment  EditableOrderAdapter androidx.fragment.app.Fragment  FragmentAddOrderBinding androidx.fragment.app.Fragment  FragmentEditOrderBinding androidx.fragment.app.Fragment  	ImageView androidx.fragment.app.Fragment  LinearLayoutManager androidx.fragment.app.Fragment  MainActivity androidx.fragment.app.Fragment  OrderDetailFragment androidx.fragment.app.Fragment  OrderPending androidx.fragment.app.Fragment  OrderPendingAdapter androidx.fragment.app.Fragment  OrderViewModel androidx.fragment.app.Fragment  PendingReceiptFragment androidx.fragment.app.Fragment  PlaceholderFragment androidx.fragment.app.Fragment  R androidx.fragment.app.Fragment  RecyclerView androidx.fragment.app.Fragment  TextView androidx.fragment.app.Fragment  Toast androidx.fragment.app.Fragment  UUID androidx.fragment.app.Fragment  Uri androidx.fragment.app.Fragment  View androidx.fragment.app.Fragment  ViewModelProvider androidx.fragment.app.Fragment  activity androidx.fragment.app.Fragment  activityViewModels androidx.fragment.app.Fragment  apply androidx.fragment.app.Fragment  	arguments androidx.fragment.app.Fragment  childFragmentManager androidx.fragment.app.Fragment  context androidx.fragment.app.Fragment  editableAdapter androidx.fragment.app.Fragment  	emptyList androidx.fragment.app.Fragment  getValue androidx.fragment.app.Fragment  java androidx.fragment.app.Fragment  joinToString androidx.fragment.app.Fragment  let androidx.fragment.app.Fragment  map androidx.fragment.app.Fragment  newInstance androidx.fragment.app.Fragment  onCreate androidx.fragment.app.Fragment  
onDestroyView androidx.fragment.app.Fragment  
onViewCreated androidx.fragment.app.Fragment  parentFragmentManager androidx.fragment.app.Fragment  provideDelegate androidx.fragment.app.Fragment  registerForActivityResult androidx.fragment.app.Fragment  requireActivity androidx.fragment.app.Fragment  requireContext androidx.fragment.app.Fragment  split androidx.fragment.app.Fragment  toUri androidx.fragment.app.Fragment  trim androidx.fragment.app.Fragment  view androidx.fragment.app.Fragment  viewLifecycleOwner androidx.fragment.app.Fragment  	viewModel androidx.fragment.app.Fragment  OnButtonClickListener 2androidx.fragment.app.Fragment.OrderPendingAdapter  ActivityMainBinding &androidx.fragment.app.FragmentActivity  AllOrdersFragment &androidx.fragment.app.FragmentActivity  OrderDetailFragment &androidx.fragment.app.FragmentActivity  OrderViewModel &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  ViewModelProvider &androidx.fragment.app.FragmentActivity  android &androidx.fragment.app.FragmentActivity  getSystemService &androidx.fragment.app.FragmentActivity  java &androidx.fragment.app.FragmentActivity  newInstance &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  supportFragmentManager &androidx.fragment.app.FragmentActivity  OnBackStackChangedListener %androidx.fragment.app.FragmentManager  addOnBackStackChangedListener %androidx.fragment.app.FragmentManager  backStackEntryCount %androidx.fragment.app.FragmentManager  beginTransaction %androidx.fragment.app.FragmentManager  popBackStack %androidx.fragment.app.FragmentManager  <SAM-CONSTRUCTOR> @androidx.fragment.app.FragmentManager.OnBackStackChangedListener  add )androidx.fragment.app.FragmentTransaction  addToBackStack )androidx.fragment.app.FragmentTransaction  commit )androidx.fragment.app.FragmentTransaction  hide )androidx.fragment.app.FragmentTransaction  replace )androidx.fragment.app.FragmentTransaction  AndroidViewModel androidx.lifecycle  LifecycleOwner androidx.lifecycle  LiveData androidx.lifecycle  MutableLiveData androidx.lifecycle  Observer androidx.lifecycle  ViewModelProvider androidx.lifecycle  
asLiveData androidx.lifecycle  viewModelScope androidx.lifecycle  observe androidx.lifecycle.LiveData  value androidx.lifecycle.LiveData  	postValue "androidx.lifecycle.MutableLiveData  value "androidx.lifecycle.MutableLiveData  <SAM-CONSTRUCTOR> androidx.lifecycle.Observer  get $androidx.lifecycle.ViewModelProvider  DiffUtil androidx.recyclerview.widget  LinearLayoutManager androidx.recyclerview.widget  ListAdapter androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  ItemCallback %androidx.recyclerview.widget.DiffUtil  ItemOrderEditableBinding (androidx.recyclerview.widget.ListAdapter  LayoutInflater (androidx.recyclerview.widget.ListAdapter  OrderViewHolder (androidx.recyclerview.widget.ListAdapter  Adapter )androidx.recyclerview.widget.RecyclerView  
LayoutManager )androidx.recyclerview.widget.RecyclerView  LinearLayoutManager )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  adapter )androidx.recyclerview.widget.RecyclerView  apply )androidx.recyclerview.widget.RecyclerView  context )androidx.recyclerview.widget.RecyclerView  editableAdapter )androidx.recyclerview.widget.RecyclerView  
layoutManager )androidx.recyclerview.widget.RecyclerView  ItemOrderEditableBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  LayoutInflater 1androidx.recyclerview.widget.RecyclerView.Adapter  OrderViewHolder 1androidx.recyclerview.widget.RecyclerView.Adapter  R 1androidx.recyclerview.widget.RecyclerView.Adapter  TextView 1androidx.recyclerview.widget.RecyclerView.Adapter  	ViewGroup 1androidx.recyclerview.widget.RecyclerView.Adapter  
ViewHolder 1androidx.recyclerview.widget.RecyclerView.Adapter  forEachIndexed 1androidx.recyclerview.widget.RecyclerView.Adapter  replace 1androidx.recyclerview.widget.RecyclerView.Adapter  MarginLayoutParams ;androidx.recyclerview.widget.RecyclerView.Adapter.ViewGroup  R 4androidx.recyclerview.widget.RecyclerView.ViewHolder  itemView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Flow 
androidx.room  Insert 
androidx.room  List 
androidx.room  OnConflictStrategy 
androidx.room  OrderEntity 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  String 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  	Companion  androidx.room.OnConflictStrategy  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  databaseBuilder androidx.room.Room  AppDatabase androidx.room.RoomDatabase  Builder androidx.room.RoomDatabase  	Companion androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  OrderDao androidx.room.RoomDatabase  Room androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  databaseBuilder androidx.room.RoomDatabase  java androidx.room.RoomDatabase  synchronized androidx.room.RoomDatabase  build "androidx.room.RoomDatabase.Builder  fallbackToDestructiveMigration "androidx.room.RoomDatabase.Builder  AppDatabase $androidx.room.RoomDatabase.Companion  Room $androidx.room.RoomDatabase.Companion  databaseBuilder $androidx.room.RoomDatabase.Companion  java $androidx.room.RoomDatabase.Companion  synchronized $androidx.room.RoomDatabase.Companion  ActivityMainBinding com.example.taobao  AllOrdersFragment com.example.taobao  AppCompatActivity com.example.taobao  Application com.example.taobao  Bundle com.example.taobao  Fragment com.example.taobao  MainActivity com.example.taobao  OrderDetailFragment com.example.taobao  OrderViewModel com.example.taobao  R com.example.taobao  String com.example.taobao  TaobaoApplication com.example.taobao  ViewModelProvider com.example.taobao  android com.example.taobao  java com.example.taobao  newInstance com.example.taobao  ActivityMainBinding com.example.taobao.MainActivity  AllOrdersFragment com.example.taobao.MainActivity  OrderDetailFragment com.example.taobao.MainActivity  OrderViewModel com.example.taobao.MainActivity  R com.example.taobao.MainActivity  ViewModelProvider com.example.taobao.MainActivity  activeFragment com.example.taobao.MainActivity  allOrdersFragment com.example.taobao.MainActivity  android com.example.taobao.MainActivity  binding com.example.taobao.MainActivity  java com.example.taobao.MainActivity  layoutInflater com.example.taobao.MainActivity  navigateToOrderDetail com.example.taobao.MainActivity  newInstance com.example.taobao.MainActivity  setContentView com.example.taobao.MainActivity  setupBackStackListener com.example.taobao.MainActivity  supportFragmentManager com.example.taobao.MainActivity  	viewModel com.example.taobao.MainActivity  tag_margin_start com.example.taobao.R.dimen  ic_launcher_background com.example.taobao.R.drawable  test com.example.taobao.R.drawable  btnApplyForInvoice com.example.taobao.R.id  btnModifyAddress com.example.taobao.R.id  btnUrgeShipment com.example.taobao.R.id  btn_copy_order_number com.example.taobao.R.id  et_buyer_address com.example.taobao.R.id  
et_buyer_name com.example.taobao.R.id  et_creation_time com.example.taobao.R.id  et_order_number com.example.taobao.R.id  et_order_status_text com.example.taobao.R.id  et_payment_time com.example.taobao.R.id  et_product_name_detail com.example.taobao.R.id  et_product_price_detail com.example.taobao.R.id  et_quantity_detail com.example.taobao.R.id  et_store_name_detail com.example.taobao.R.id  et_store_rating com.example.taobao.R.id  et_wechat_transaction_id com.example.taobao.R.id  filter_add_order com.example.taobao.R.id  
filter_all com.example.taobao.R.id  filter_edit_order com.example.taobao.R.id  filter_pending_payment com.example.taobao.R.id  filter_pending_receipt com.example.taobao.R.id  filter_pending_shipment com.example.taobao.R.id  
filter_refund com.example.taobao.R.id  
filter_review com.example.taobao.R.id  fragment_container com.example.taobao.R.id  	ivProduct com.example.taobao.R.id  iv_back com.example.taobao.R.id  iv_product_image_detail com.example.taobao.R.id  iv_store_icon_detail com.example.taobao.R.id  orderRecyclerView com.example.taobao.R.id  order_list_container com.example.taobao.R.id  placeholder_text com.example.taobao.R.id  tags_container com.example.taobao.R.id  
tvOrderStatus com.example.taobao.R.id  tvPayAmount com.example.taobao.R.id  
tvProductDesc com.example.taobao.R.id  
tvProductName com.example.taobao.R.id  tvProductPrice com.example.taobao.R.id  
tvQuantity com.example.taobao.R.id  tvShippingPromise com.example.taobao.R.id  tvShippingStatus com.example.taobao.R.id  
tvShopName com.example.taobao.R.id  tv_order_services com.example.taobao.R.id  tv_total_payment com.example.taobao.R.id  fragment_all_orders com.example.taobao.R.layout  fragment_order_detail com.example.taobao.R.layout  fragment_pending_receipt com.example.taobao.R.layout  fragment_placeholder com.example.taobao.R.layout  item_order_pending com.example.taobao.R.layout  item_tag com.example.taobao.R.layout  AppDatabase com.example.taobao.data  Context com.example.taobao.data  Dao com.example.taobao.data  Database com.example.taobao.data  Delete com.example.taobao.data  Entity com.example.taobao.data  Flow com.example.taobao.data  Insert com.example.taobao.data  Int com.example.taobao.data  List com.example.taobao.data  OnConflictStrategy com.example.taobao.data  OrderDao com.example.taobao.data  OrderEntity com.example.taobao.data  OrderPending com.example.taobao.data  OrderRepository com.example.taobao.data  
PrimaryKey com.example.taobao.data  Query com.example.taobao.data  Room com.example.taobao.data  RoomDatabase com.example.taobao.data  String com.example.taobao.data  
TagsConverter com.example.taobao.data  
TypeConverter com.example.taobao.data  TypeConverters com.example.taobao.data  Update com.example.taobao.data  Volatile com.example.taobao.data  databaseBuilder com.example.taobao.data  	emptyList com.example.taobao.data  fromOrderPending com.example.taobao.data  isEmpty com.example.taobao.data  java com.example.taobao.data  joinToString com.example.taobao.data  map com.example.taobao.data  split com.example.taobao.data  synchronized com.example.taobao.data  trim com.example.taobao.data  AppDatabase #com.example.taobao.data.AppDatabase  	Companion #com.example.taobao.data.AppDatabase  Context #com.example.taobao.data.AppDatabase  INSTANCE #com.example.taobao.data.AppDatabase  OrderDao #com.example.taobao.data.AppDatabase  Room #com.example.taobao.data.AppDatabase  Volatile #com.example.taobao.data.AppDatabase  databaseBuilder #com.example.taobao.data.AppDatabase  getDatabase #com.example.taobao.data.AppDatabase  java #com.example.taobao.data.AppDatabase  orderDao #com.example.taobao.data.AppDatabase  synchronized #com.example.taobao.data.AppDatabase  AppDatabase -com.example.taobao.data.AppDatabase.Companion  INSTANCE -com.example.taobao.data.AppDatabase.Companion  Room -com.example.taobao.data.AppDatabase.Companion  databaseBuilder -com.example.taobao.data.AppDatabase.Companion  getDatabase -com.example.taobao.data.AppDatabase.Companion  java -com.example.taobao.data.AppDatabase.Companion  synchronized -com.example.taobao.data.AppDatabase.Companion  OnConflictStrategy  com.example.taobao.data.OrderDao  deleteAllOrders  com.example.taobao.data.OrderDao  deleteOrder  com.example.taobao.data.OrderDao  deleteOrderById  com.example.taobao.data.OrderDao  getAllOrders  com.example.taobao.data.OrderDao  getOrderById  com.example.taobao.data.OrderDao  insertOrder  com.example.taobao.data.OrderDao  insertOrders  com.example.taobao.data.OrderDao  updateOrder  com.example.taobao.data.OrderDao  	Companion #com.example.taobao.data.OrderEntity  Int #com.example.taobao.data.OrderEntity  List #com.example.taobao.data.OrderEntity  OrderEntity #com.example.taobao.data.OrderEntity  OrderPending #com.example.taobao.data.OrderEntity  
PrimaryKey #com.example.taobao.data.OrderEntity  String #com.example.taobao.data.OrderEntity  
TagsConverter #com.example.taobao.data.OrderEntity  TypeConverters #com.example.taobao.data.OrderEntity  buyerAddress #com.example.taobao.data.OrderEntity  	buyerName #com.example.taobao.data.OrderEntity  creationTime #com.example.taobao.data.OrderEntity  fromOrderPending #com.example.taobao.data.OrderEntity  id #com.example.taobao.data.OrderEntity  	logistics #com.example.taobao.data.OrderEntity  orderNumber #com.example.taobao.data.OrderEntity  
orderServices #com.example.taobao.data.OrderEntity  orderStatus #com.example.taobao.data.OrderEntity  orderStatusText #com.example.taobao.data.OrderEntity  payInfo #com.example.taobao.data.OrderEntity  paymentTime #com.example.taobao.data.OrderEntity  productDesc #com.example.taobao.data.OrderEntity  
productImg #com.example.taobao.data.OrderEntity  productName #com.example.taobao.data.OrderEntity  productPrice #com.example.taobao.data.OrderEntity  quantity #com.example.taobao.data.OrderEntity  shippingPromise #com.example.taobao.data.OrderEntity  shippingStatus #com.example.taobao.data.OrderEntity  shopIconUri #com.example.taobao.data.OrderEntity  shopName #com.example.taobao.data.OrderEntity  storeRating #com.example.taobao.data.OrderEntity  tags #com.example.taobao.data.OrderEntity  tmallPoints #com.example.taobao.data.OrderEntity  toOrderPending #com.example.taobao.data.OrderEntity  transactionSnapshotText #com.example.taobao.data.OrderEntity  wechatTransactionId #com.example.taobao.data.OrderEntity  OrderEntity -com.example.taobao.data.OrderEntity.Companion  OrderPending -com.example.taobao.data.OrderEntity.Companion  
TagsConverter -com.example.taobao.data.OrderEntity.Companion  fromOrderPending -com.example.taobao.data.OrderEntity.Companion  OrderEntity 'com.example.taobao.data.OrderRepository  	allOrders 'com.example.taobao.data.OrderRepository  deleteOrder 'com.example.taobao.data.OrderRepository  fromOrderPending 'com.example.taobao.data.OrderRepository  getOrderById 'com.example.taobao.data.OrderRepository  insertOrder 'com.example.taobao.data.OrderRepository  map 'com.example.taobao.data.OrderRepository  orderDao 'com.example.taobao.data.OrderRepository  updateOrder 'com.example.taobao.data.OrderRepository  	emptyList %com.example.taobao.data.TagsConverter  isEmpty %com.example.taobao.data.TagsConverter  joinToString %com.example.taobao.data.TagsConverter  map %com.example.taobao.data.TagsConverter  split %com.example.taobao.data.TagsConverter  trim %com.example.taobao.data.TagsConverter  ActivityMainBinding com.example.taobao.databinding  FragmentAddOrderBinding com.example.taobao.databinding  FragmentEditOrderBinding com.example.taobao.databinding  ItemOrderEditableBinding com.example.taobao.databinding  headerContainer 2com.example.taobao.databinding.ActivityMainBinding  inflate 2com.example.taobao.databinding.ActivityMainBinding  root 2com.example.taobao.databinding.ActivityMainBinding  btnSaveOrder 6com.example.taobao.databinding.FragmentAddOrderBinding  btnUploadImage 6com.example.taobao.databinding.FragmentAddOrderBinding  btnUploadShopIcon 6com.example.taobao.databinding.FragmentAddOrderBinding  etBuyerAddress 6com.example.taobao.databinding.FragmentAddOrderBinding  etBuyerName 6com.example.taobao.databinding.FragmentAddOrderBinding  etCreationTime 6com.example.taobao.databinding.FragmentAddOrderBinding  etLogistics 6com.example.taobao.databinding.FragmentAddOrderBinding  
etOrderNumber 6com.example.taobao.databinding.FragmentAddOrderBinding  etOrderServices 6com.example.taobao.databinding.FragmentAddOrderBinding  
etOrderStatus 6com.example.taobao.databinding.FragmentAddOrderBinding  etOrderStatusText 6com.example.taobao.databinding.FragmentAddOrderBinding  	etPayInfo 6com.example.taobao.databinding.FragmentAddOrderBinding  
etPaymentTime 6com.example.taobao.databinding.FragmentAddOrderBinding  
etProductDesc 6com.example.taobao.databinding.FragmentAddOrderBinding  
etProductName 6com.example.taobao.databinding.FragmentAddOrderBinding  etProductPrice 6com.example.taobao.databinding.FragmentAddOrderBinding  
etQuantity 6com.example.taobao.databinding.FragmentAddOrderBinding  etShippingPromise 6com.example.taobao.databinding.FragmentAddOrderBinding  etShippingStatus 6com.example.taobao.databinding.FragmentAddOrderBinding  
etShopName 6com.example.taobao.databinding.FragmentAddOrderBinding  
etStoreRating 6com.example.taobao.databinding.FragmentAddOrderBinding  etTags 6com.example.taobao.databinding.FragmentAddOrderBinding  
etTmallPoints 6com.example.taobao.databinding.FragmentAddOrderBinding  etTransactionSnapshot 6com.example.taobao.databinding.FragmentAddOrderBinding  etWechatTransactionId 6com.example.taobao.databinding.FragmentAddOrderBinding  inflate 6com.example.taobao.databinding.FragmentAddOrderBinding  ivProductImage 6com.example.taobao.databinding.FragmentAddOrderBinding  
ivShopIcon 6com.example.taobao.databinding.FragmentAddOrderBinding  root 6com.example.taobao.databinding.FragmentAddOrderBinding  editOrderRecyclerView 7com.example.taobao.databinding.FragmentEditOrderBinding  inflate 7com.example.taobao.databinding.FragmentEditOrderBinding  root 7com.example.taobao.databinding.FragmentEditOrderBinding  	btnDelete 7com.example.taobao.databinding.ItemOrderEditableBinding  btnEdit 7com.example.taobao.databinding.ItemOrderEditableBinding  inflate 7com.example.taobao.databinding.ItemOrderEditableBinding  root 7com.example.taobao.databinding.ItemOrderEditableBinding  
tvProductName 7com.example.taobao.databinding.ItemOrderEditableBinding  
tvShopName 7com.example.taobao.databinding.ItemOrderEditableBinding  ARG_MESSAGE com.example.taobao.ui  ARG_ORDER_ID com.example.taobao.ui  ActivityResultContracts com.example.taobao.ui  AddOrderFragment com.example.taobao.ui  AllOrdersFragment com.example.taobao.ui  AndroidViewModel com.example.taobao.ui  AppDatabase com.example.taobao.ui  Application com.example.taobao.ui  Boolean com.example.taobao.ui  Bundle com.example.taobao.ui  Button com.example.taobao.ui  ClipData com.example.taobao.ui  ClipboardManager com.example.taobao.ui  Context com.example.taobao.ui  DiffUtil com.example.taobao.ui  EditOrderFragment com.example.taobao.ui  EditText com.example.taobao.ui  EditableOrderAdapter com.example.taobao.ui  Fragment com.example.taobao.ui  FragmentAddOrderBinding com.example.taobao.ui  FragmentEditOrderBinding com.example.taobao.ui  	ImageView com.example.taobao.ui  Int com.example.taobao.ui  ItemOrderEditableBinding com.example.taobao.ui  LayoutInflater com.example.taobao.ui  LinearLayout com.example.taobao.ui  LinearLayoutManager com.example.taobao.ui  List com.example.taobao.ui  ListAdapter com.example.taobao.ui  LiveData com.example.taobao.ui  MainActivity com.example.taobao.ui  MutableLiveData com.example.taobao.ui  OnButtonClickListener com.example.taobao.ui  OrderDetailFragment com.example.taobao.ui  OrderPending com.example.taobao.ui  OrderPendingAdapter com.example.taobao.ui  OrderRepository com.example.taobao.ui  OrderViewHolder com.example.taobao.ui  OrderViewModel com.example.taobao.ui  PendingReceiptFragment com.example.taobao.ui  PlaceholderFragment com.example.taobao.ui  R com.example.taobao.ui  RecyclerView com.example.taobao.ui  String com.example.taobao.ui  TextView com.example.taobao.ui  Toast com.example.taobao.ui  UUID com.example.taobao.ui  Unit com.example.taobao.ui  Uri com.example.taobao.ui  View com.example.taobao.ui  	ViewGroup com.example.taobao.ui  
ViewHolder com.example.taobao.ui  ViewModelProvider com.example.taobao.ui  _navigateToPendingReceipt com.example.taobao.ui  _selectedOrderDetails com.example.taobao.ui  activity com.example.taobao.ui  apply com.example.taobao.ui  
asLiveData com.example.taobao.ui  context com.example.taobao.ui  editableAdapter com.example.taobao.ui  	emptyList com.example.taobao.ui  forEachIndexed com.example.taobao.ui  getDatabase com.example.taobao.ui  getInitialOrder com.example.taobao.ui  getValue com.example.taobao.ui  
isNullOrEmpty com.example.taobao.ui  java com.example.taobao.ui  joinToString com.example.taobao.ui  launch com.example.taobao.ui  let com.example.taobao.ui  listOf com.example.taobao.ui  map com.example.taobao.ui  newInstance com.example.taobao.ui  orders com.example.taobao.ui  provideDelegate com.example.taobao.ui  replace com.example.taobao.ui  
repository com.example.taobao.ui  split com.example.taobao.ui  toUri com.example.taobao.ui  trim com.example.taobao.ui  	viewModel com.example.taobao.ui  ActivityResultContracts &com.example.taobao.ui.AddOrderFragment  FragmentAddOrderBinding &com.example.taobao.ui.AddOrderFragment  OrderPending &com.example.taobao.ui.AddOrderFragment  OrderViewModel &com.example.taobao.ui.AddOrderFragment  R &com.example.taobao.ui.AddOrderFragment  Toast &com.example.taobao.ui.AddOrderFragment  UUID &com.example.taobao.ui.AddOrderFragment  Uri &com.example.taobao.ui.AddOrderFragment  ViewModelProvider &com.example.taobao.ui.AddOrderFragment  _binding &com.example.taobao.ui.AddOrderFragment  binding &com.example.taobao.ui.AddOrderFragment  context &com.example.taobao.ui.AddOrderFragment  editingOrder &com.example.taobao.ui.AddOrderFragment  imageUri &com.example.taobao.ui.AddOrderFragment  java &com.example.taobao.ui.AddOrderFragment  joinToString &com.example.taobao.ui.AddOrderFragment  let &com.example.taobao.ui.AddOrderFragment  map &com.example.taobao.ui.AddOrderFragment  pickImageLauncher &com.example.taobao.ui.AddOrderFragment  pickShopIconLauncher &com.example.taobao.ui.AddOrderFragment  populateForm &com.example.taobao.ui.AddOrderFragment  registerForActivityResult &com.example.taobao.ui.AddOrderFragment  requireActivity &com.example.taobao.ui.AddOrderFragment  	saveOrder &com.example.taobao.ui.AddOrderFragment  shopIconUri &com.example.taobao.ui.AddOrderFragment  split &com.example.taobao.ui.AddOrderFragment  trim &com.example.taobao.ui.AddOrderFragment  viewLifecycleOwner &com.example.taobao.ui.AddOrderFragment  	viewModel &com.example.taobao.ui.AddOrderFragment  AddOrderFragment 'com.example.taobao.ui.AllOrdersFragment  EditOrderFragment 'com.example.taobao.ui.AllOrdersFragment  PendingReceiptFragment 'com.example.taobao.ui.AllOrdersFragment  PlaceholderFragment 'com.example.taobao.ui.AllOrdersFragment  R 'com.example.taobao.ui.AllOrdersFragment  activityViewModels 'com.example.taobao.ui.AllOrdersFragment  addOrderFragment 'com.example.taobao.ui.AllOrdersFragment  childFragmentManager 'com.example.taobao.ui.AllOrdersFragment  editOrderFragment 'com.example.taobao.ui.AllOrdersFragment  getValue 'com.example.taobao.ui.AllOrdersFragment  newInstance 'com.example.taobao.ui.AllOrdersFragment  pendingReceiptFragment 'com.example.taobao.ui.AllOrdersFragment  placeholderFragment 'com.example.taobao.ui.AllOrdersFragment  provideDelegate 'com.example.taobao.ui.AllOrdersFragment  setupFilterClicks 'com.example.taobao.ui.AllOrdersFragment  switchFragment 'com.example.taobao.ui.AllOrdersFragment  ItemCallback com.example.taobao.ui.DiffUtil  EditableOrderAdapter 'com.example.taobao.ui.EditOrderFragment  FragmentEditOrderBinding 'com.example.taobao.ui.EditOrderFragment  LinearLayoutManager 'com.example.taobao.ui.EditOrderFragment  OrderViewModel 'com.example.taobao.ui.EditOrderFragment  ViewModelProvider 'com.example.taobao.ui.EditOrderFragment  _binding 'com.example.taobao.ui.EditOrderFragment  apply 'com.example.taobao.ui.EditOrderFragment  binding 'com.example.taobao.ui.EditOrderFragment  editableAdapter 'com.example.taobao.ui.EditOrderFragment  java 'com.example.taobao.ui.EditOrderFragment  requireActivity 'com.example.taobao.ui.EditOrderFragment  viewLifecycleOwner 'com.example.taobao.ui.EditOrderFragment  	viewModel 'com.example.taobao.ui.EditOrderFragment  Boolean *com.example.taobao.ui.EditableOrderAdapter  DiffUtil *com.example.taobao.ui.EditableOrderAdapter  Int *com.example.taobao.ui.EditableOrderAdapter  ItemOrderEditableBinding *com.example.taobao.ui.EditableOrderAdapter  LayoutInflater *com.example.taobao.ui.EditableOrderAdapter  OrderDiffCallback *com.example.taobao.ui.EditableOrderAdapter  OrderPending *com.example.taobao.ui.EditableOrderAdapter  OrderViewHolder *com.example.taobao.ui.EditableOrderAdapter  RecyclerView *com.example.taobao.ui.EditableOrderAdapter  Unit *com.example.taobao.ui.EditableOrderAdapter  	ViewGroup *com.example.taobao.ui.EditableOrderAdapter  getItem *com.example.taobao.ui.EditableOrderAdapter  
onDeleteClick *com.example.taobao.ui.EditableOrderAdapter  onEditClick *com.example.taobao.ui.EditableOrderAdapter  
submitList *com.example.taobao.ui.EditableOrderAdapter  ItemCallback 3com.example.taobao.ui.EditableOrderAdapter.DiffUtil  bind :com.example.taobao.ui.EditableOrderAdapter.OrderViewHolder  binding :com.example.taobao.ui.EditableOrderAdapter.OrderViewHolder  
ViewHolder 7com.example.taobao.ui.EditableOrderAdapter.RecyclerView  ARG_ORDER_ID )com.example.taobao.ui.OrderDetailFragment  Bundle )com.example.taobao.ui.OrderDetailFragment  Button )com.example.taobao.ui.OrderDetailFragment  ClipData )com.example.taobao.ui.OrderDetailFragment  ClipboardManager )com.example.taobao.ui.OrderDetailFragment  	Companion )com.example.taobao.ui.OrderDetailFragment  Context )com.example.taobao.ui.OrderDetailFragment  EditText )com.example.taobao.ui.OrderDetailFragment  	ImageView )com.example.taobao.ui.OrderDetailFragment  LayoutInflater )com.example.taobao.ui.OrderDetailFragment  OrderDetailFragment )com.example.taobao.ui.OrderDetailFragment  OrderPending )com.example.taobao.ui.OrderDetailFragment  OrderViewModel )com.example.taobao.ui.OrderDetailFragment  R )com.example.taobao.ui.OrderDetailFragment  String )com.example.taobao.ui.OrderDetailFragment  TextView )com.example.taobao.ui.OrderDetailFragment  Toast )com.example.taobao.ui.OrderDetailFragment  View )com.example.taobao.ui.OrderDetailFragment  	ViewGroup )com.example.taobao.ui.OrderDetailFragment  activityViewModels )com.example.taobao.ui.OrderDetailFragment  	arguments )com.example.taobao.ui.OrderDetailFragment  	bindViews )com.example.taobao.ui.OrderDetailFragment  btnCopyOrderNumber )com.example.taobao.ui.OrderDetailFragment  context )com.example.taobao.ui.OrderDetailFragment  currentOrder )com.example.taobao.ui.OrderDetailFragment  etBuyerAddress )com.example.taobao.ui.OrderDetailFragment  etBuyerName )com.example.taobao.ui.OrderDetailFragment  etCreationTime )com.example.taobao.ui.OrderDetailFragment  
etOrderNumber )com.example.taobao.ui.OrderDetailFragment  etOrderStatusText )com.example.taobao.ui.OrderDetailFragment  
etPaymentTime )com.example.taobao.ui.OrderDetailFragment  
etProductName )com.example.taobao.ui.OrderDetailFragment  etProductPrice )com.example.taobao.ui.OrderDetailFragment  
etQuantity )com.example.taobao.ui.OrderDetailFragment  etStoreName )com.example.taobao.ui.OrderDetailFragment  
etStoreRating )com.example.taobao.ui.OrderDetailFragment  etWechatTransactionId )com.example.taobao.ui.OrderDetailFragment  getValue )com.example.taobao.ui.OrderDetailFragment  ivProductImage )com.example.taobao.ui.OrderDetailFragment  let )com.example.taobao.ui.OrderDetailFragment  newInstance )com.example.taobao.ui.OrderDetailFragment  orderId )com.example.taobao.ui.OrderDetailFragment  parentFragmentManager )com.example.taobao.ui.OrderDetailFragment  
populateUi )com.example.taobao.ui.OrderDetailFragment  provideDelegate )com.example.taobao.ui.OrderDetailFragment  requireActivity )com.example.taobao.ui.OrderDetailFragment  requireContext )com.example.taobao.ui.OrderDetailFragment  toUri )com.example.taobao.ui.OrderDetailFragment  tvOrderServices )com.example.taobao.ui.OrderDetailFragment  tvTotalPayment )com.example.taobao.ui.OrderDetailFragment  view )com.example.taobao.ui.OrderDetailFragment  viewLifecycleOwner )com.example.taobao.ui.OrderDetailFragment  	viewModel )com.example.taobao.ui.OrderDetailFragment  ARG_ORDER_ID 3com.example.taobao.ui.OrderDetailFragment.Companion  Bundle 3com.example.taobao.ui.OrderDetailFragment.Companion  ClipData 3com.example.taobao.ui.OrderDetailFragment.Companion  Context 3com.example.taobao.ui.OrderDetailFragment.Companion  OrderDetailFragment 3com.example.taobao.ui.OrderDetailFragment.Companion  R 3com.example.taobao.ui.OrderDetailFragment.Companion  Toast 3com.example.taobao.ui.OrderDetailFragment.Companion  activityViewModels 3com.example.taobao.ui.OrderDetailFragment.Companion  getValue 3com.example.taobao.ui.OrderDetailFragment.Companion  let 3com.example.taobao.ui.OrderDetailFragment.Companion  newInstance 3com.example.taobao.ui.OrderDetailFragment.Companion  provideDelegate 3com.example.taobao.ui.OrderDetailFragment.Companion  toUri 3com.example.taobao.ui.OrderDetailFragment.Companion  buyerAddress "com.example.taobao.ui.OrderPending  	buyerName "com.example.taobao.ui.OrderPending  copy "com.example.taobao.ui.OrderPending  creationTime "com.example.taobao.ui.OrderPending  id "com.example.taobao.ui.OrderPending  let "com.example.taobao.ui.OrderPending  	logistics "com.example.taobao.ui.OrderPending  orderNumber "com.example.taobao.ui.OrderPending  
orderServices "com.example.taobao.ui.OrderPending  orderStatus "com.example.taobao.ui.OrderPending  orderStatusText "com.example.taobao.ui.OrderPending  payInfo "com.example.taobao.ui.OrderPending  paymentTime "com.example.taobao.ui.OrderPending  productDesc "com.example.taobao.ui.OrderPending  
productImg "com.example.taobao.ui.OrderPending  productName "com.example.taobao.ui.OrderPending  productPrice "com.example.taobao.ui.OrderPending  quantity "com.example.taobao.ui.OrderPending  shippingPromise "com.example.taobao.ui.OrderPending  shippingStatus "com.example.taobao.ui.OrderPending  shopIconUri "com.example.taobao.ui.OrderPending  shopName "com.example.taobao.ui.OrderPending  storeRating "com.example.taobao.ui.OrderPending  tags "com.example.taobao.ui.OrderPending  tmallPoints "com.example.taobao.ui.OrderPending  transactionSnapshotText "com.example.taobao.ui.OrderPending  wechatTransactionId "com.example.taobao.ui.OrderPending  Button )com.example.taobao.ui.OrderPendingAdapter  	ImageView )com.example.taobao.ui.OrderPendingAdapter  Int )com.example.taobao.ui.OrderPendingAdapter  LayoutInflater )com.example.taobao.ui.OrderPendingAdapter  LinearLayout )com.example.taobao.ui.OrderPendingAdapter  List )com.example.taobao.ui.OrderPendingAdapter  OnButtonClickListener )com.example.taobao.ui.OrderPendingAdapter  OrderPending )com.example.taobao.ui.OrderPendingAdapter  R )com.example.taobao.ui.OrderPendingAdapter  RecyclerView )com.example.taobao.ui.OrderPendingAdapter  TextView )com.example.taobao.ui.OrderPendingAdapter  View )com.example.taobao.ui.OrderPendingAdapter  	ViewGroup )com.example.taobao.ui.OrderPendingAdapter  
ViewHolder )com.example.taobao.ui.OrderPendingAdapter  buttonClickListener )com.example.taobao.ui.OrderPendingAdapter  forEachIndexed )com.example.taobao.ui.OrderPendingAdapter  notifyDataSetChanged )com.example.taobao.ui.OrderPendingAdapter  orders )com.example.taobao.ui.OrderPendingAdapter  replace )com.example.taobao.ui.OrderPendingAdapter  setOnButtonClickListener )com.example.taobao.ui.OrderPendingAdapter  updateOrders )com.example.taobao.ui.OrderPendingAdapter  onApplyForInvoiceClick ?com.example.taobao.ui.OrderPendingAdapter.OnButtonClickListener  onModifyAddressClick ?com.example.taobao.ui.OrderPendingAdapter.OnButtonClickListener  onUrgeShipmentClick ?com.example.taobao.ui.OrderPendingAdapter.OnButtonClickListener  
ViewHolder 6com.example.taobao.ui.OrderPendingAdapter.RecyclerView  MarginLayoutParams 3com.example.taobao.ui.OrderPendingAdapter.ViewGroup  R 4com.example.taobao.ui.OrderPendingAdapter.ViewHolder  btnApplyForInvoice 4com.example.taobao.ui.OrderPendingAdapter.ViewHolder  btnModifyAddress 4com.example.taobao.ui.OrderPendingAdapter.ViewHolder  btnUrgeShipment 4com.example.taobao.ui.OrderPendingAdapter.ViewHolder  itemView 4com.example.taobao.ui.OrderPendingAdapter.ViewHolder  	ivProduct 4com.example.taobao.ui.OrderPendingAdapter.ViewHolder  
tagsContainer 4com.example.taobao.ui.OrderPendingAdapter.ViewHolder  
tvOrderStatus 4com.example.taobao.ui.OrderPendingAdapter.ViewHolder  tvPayAmount 4com.example.taobao.ui.OrderPendingAdapter.ViewHolder  
tvProductDesc 4com.example.taobao.ui.OrderPendingAdapter.ViewHolder  
tvProductName 4com.example.taobao.ui.OrderPendingAdapter.ViewHolder  tvProductPrice 4com.example.taobao.ui.OrderPendingAdapter.ViewHolder  
tvQuantity 4com.example.taobao.ui.OrderPendingAdapter.ViewHolder  tvShippingPromise 4com.example.taobao.ui.OrderPendingAdapter.ViewHolder  tvShippingStatus 4com.example.taobao.ui.OrderPendingAdapter.ViewHolder  
tvShopName 4com.example.taobao.ui.OrderPendingAdapter.ViewHolder  AppDatabase $com.example.taobao.ui.OrderViewModel  MutableLiveData $com.example.taobao.ui.OrderViewModel  OrderPending $com.example.taobao.ui.OrderViewModel  OrderRepository $com.example.taobao.ui.OrderViewModel  R $com.example.taobao.ui.OrderViewModel  _navigateToEditOrder $com.example.taobao.ui.OrderViewModel  _navigateToPendingReceipt $com.example.taobao.ui.OrderViewModel  _selectedOrderDetails $com.example.taobao.ui.OrderViewModel  _selectedOrderForEdit $com.example.taobao.ui.OrderViewModel  addOrder $com.example.taobao.ui.OrderViewModel  
asLiveData $com.example.taobao.ui.OrderViewModel  deleteOrder $com.example.taobao.ui.OrderViewModel  getDatabase $com.example.taobao.ui.OrderViewModel  getInitialOrder $com.example.taobao.ui.OrderViewModel  
isNullOrEmpty $com.example.taobao.ui.OrderViewModel  launch $com.example.taobao.ui.OrderViewModel  listOf $com.example.taobao.ui.OrderViewModel  loadOrderDetails $com.example.taobao.ui.OrderViewModel  onOrderSelectedForEdit $com.example.taobao.ui.OrderViewModel  orders $com.example.taobao.ui.OrderViewModel  
repository $com.example.taobao.ui.OrderViewModel  selectedOrderDetails $com.example.taobao.ui.OrderViewModel  selectedOrderForEdit $com.example.taobao.ui.OrderViewModel  updateOrder $com.example.taobao.ui.OrderViewModel  viewModelScope $com.example.taobao.ui.OrderViewModel  LinearLayoutManager ,com.example.taobao.ui.PendingReceiptFragment  OrderPendingAdapter ,com.example.taobao.ui.PendingReceiptFragment  OrderViewModel ,com.example.taobao.ui.PendingReceiptFragment  R ,com.example.taobao.ui.PendingReceiptFragment  Toast ,com.example.taobao.ui.PendingReceiptFragment  ViewModelProvider ,com.example.taobao.ui.PendingReceiptFragment  activity ,com.example.taobao.ui.PendingReceiptFragment  context ,com.example.taobao.ui.PendingReceiptFragment  	emptyList ,com.example.taobao.ui.PendingReceiptFragment  java ,com.example.taobao.ui.PendingReceiptFragment  orderAdapter ,com.example.taobao.ui.PendingReceiptFragment  requireActivity ,com.example.taobao.ui.PendingReceiptFragment  viewLifecycleOwner ,com.example.taobao.ui.PendingReceiptFragment  	viewModel ,com.example.taobao.ui.PendingReceiptFragment  ARG_MESSAGE )com.example.taobao.ui.PlaceholderFragment  Bundle )com.example.taobao.ui.PlaceholderFragment  	Companion )com.example.taobao.ui.PlaceholderFragment  PlaceholderFragment )com.example.taobao.ui.PlaceholderFragment  R )com.example.taobao.ui.PlaceholderFragment  String )com.example.taobao.ui.PlaceholderFragment  TextView )com.example.taobao.ui.PlaceholderFragment  View )com.example.taobao.ui.PlaceholderFragment  	arguments )com.example.taobao.ui.PlaceholderFragment  newInstance )com.example.taobao.ui.PlaceholderFragment  ARG_MESSAGE 3com.example.taobao.ui.PlaceholderFragment.Companion  Bundle 3com.example.taobao.ui.PlaceholderFragment.Companion  PlaceholderFragment 3com.example.taobao.ui.PlaceholderFragment.Companion  R 3com.example.taobao.ui.PlaceholderFragment.Companion  newInstance 3com.example.taobao.ui.PlaceholderFragment.Companion  Adapter "com.example.taobao.ui.RecyclerView  
ViewHolder "com.example.taobao.ui.RecyclerView  MarginLayoutParams com.example.taobao.ui.ViewGroup  Boolean com.example.taobao.ui.theme  Build com.example.taobao.ui.theme  
Composable com.example.taobao.ui.theme  DarkColorScheme com.example.taobao.ui.theme  
FontFamily com.example.taobao.ui.theme  
FontWeight com.example.taobao.ui.theme  LightColorScheme com.example.taobao.ui.theme  Pink40 com.example.taobao.ui.theme  Pink80 com.example.taobao.ui.theme  Purple40 com.example.taobao.ui.theme  Purple80 com.example.taobao.ui.theme  PurpleGrey40 com.example.taobao.ui.theme  PurpleGrey80 com.example.taobao.ui.theme  TaobaoTheme com.example.taobao.ui.theme  
Typography com.example.taobao.ui.theme  Unit com.example.taobao.ui.theme  Class 	java.lang  ActivityResultContracts 	java.util  Bundle 	java.util  Fragment 	java.util  FragmentAddOrderBinding 	java.util  LayoutInflater 	java.util  OrderPending 	java.util  OrderViewModel 	java.util  R 	java.util  Toast 	java.util  UUID 	java.util  Uri 	java.util  View 	java.util  	ViewGroup 	java.util  ViewModelProvider 	java.util  java 	java.util  joinToString 	java.util  let 	java.util  map 	java.util  split 	java.util  trim 	java.util  
randomUUID java.util.UUID  toString java.util.UUID  Array kotlin  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Lazy kotlin  Result kotlin  apply kotlin  getValue kotlin  let kotlin  map kotlin  synchronized kotlin  toString 
kotlin.Any  isEmpty kotlin.CharSequence  sp 
kotlin.Double  invoke kotlin.Function1  	compareTo 
kotlin.Int  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  isEmpty 
kotlin.String  let 
kotlin.String  replace 
kotlin.String  split 
kotlin.String  toUri 
kotlin.String  trim 
kotlin.String  List kotlin.collections  	emptyList kotlin.collections  forEachIndexed kotlin.collections  getValue kotlin.collections  isEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  joinToString kotlin.collections  listOf kotlin.collections  map kotlin.collections  forEachIndexed kotlin.collections.List  get kotlin.collections.List  
isNullOrEmpty kotlin.collections.List  joinToString kotlin.collections.List  map kotlin.collections.List  size kotlin.collections.List  SuspendFunction1 kotlin.coroutines  Volatile 
kotlin.jvm  java 
kotlin.jvm  KClass kotlin.reflect  
KProperty1 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  forEachIndexed kotlin.sequences  joinToString kotlin.sequences  map kotlin.sequences  forEachIndexed kotlin.text  isEmpty kotlin.text  
isNullOrEmpty kotlin.text  map kotlin.text  replace kotlin.text  split kotlin.text  trim kotlin.text  CoroutineScope kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  _navigateToPendingReceipt !kotlinx.coroutines.CoroutineScope  _selectedOrderDetails !kotlinx.coroutines.CoroutineScope  getInitialOrder !kotlinx.coroutines.CoroutineScope  
isNullOrEmpty !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  orders !kotlinx.coroutines.CoroutineScope  
repository !kotlinx.coroutines.CoroutineScope  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  map kotlinx.coroutines.flow  
asLiveData kotlinx.coroutines.flow.Flow  collect kotlinx.coroutines.flow.Flow  map kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  
getBoolean android.os.BaseBundle  
putBoolean android.os.BaseBundle  
getBoolean android.os.Bundle  
putBoolean android.os.Bundle  ARG_SHOW_ONLY_PENDING_RECEIPT androidx.fragment.app.Fragment  filter androidx.fragment.app.Fragment  OnItemClickListener 2androidx.fragment.app.Fragment.OrderPendingAdapter  ARG_SHOW_ONLY_PENDING_RECEIPT com.example.taobao.ui  OnItemClickListener com.example.taobao.ui  filter com.example.taobao.ui  OnItemClickListener )com.example.taobao.ui.OrderPendingAdapter  itemClickListener )com.example.taobao.ui.OrderPendingAdapter  setOnItemClickListener )com.example.taobao.ui.OrderPendingAdapter  onItemClick =com.example.taobao.ui.OrderPendingAdapter.OnItemClickListener  ARG_SHOW_ONLY_PENDING_RECEIPT ,com.example.taobao.ui.PendingReceiptFragment  Boolean ,com.example.taobao.ui.PendingReceiptFragment  Bundle ,com.example.taobao.ui.PendingReceiptFragment  	Companion ,com.example.taobao.ui.PendingReceiptFragment  LayoutInflater ,com.example.taobao.ui.PendingReceiptFragment  MainActivity ,com.example.taobao.ui.PendingReceiptFragment  OrderPending ,com.example.taobao.ui.PendingReceiptFragment  PendingReceiptFragment ,com.example.taobao.ui.PendingReceiptFragment  RecyclerView ,com.example.taobao.ui.PendingReceiptFragment  View ,com.example.taobao.ui.PendingReceiptFragment  	ViewGroup ,com.example.taobao.ui.PendingReceiptFragment  	arguments ,com.example.taobao.ui.PendingReceiptFragment  filter ,com.example.taobao.ui.PendingReceiptFragment  let ,com.example.taobao.ui.PendingReceiptFragment  newInstance ,com.example.taobao.ui.PendingReceiptFragment  showOnlyPendingReceipt ,com.example.taobao.ui.PendingReceiptFragment  ARG_SHOW_ONLY_PENDING_RECEIPT 6com.example.taobao.ui.PendingReceiptFragment.Companion  Bundle 6com.example.taobao.ui.PendingReceiptFragment.Companion  LinearLayoutManager 6com.example.taobao.ui.PendingReceiptFragment.Companion  OrderPendingAdapter 6com.example.taobao.ui.PendingReceiptFragment.Companion  OrderViewModel 6com.example.taobao.ui.PendingReceiptFragment.Companion  PendingReceiptFragment 6com.example.taobao.ui.PendingReceiptFragment.Companion  R 6com.example.taobao.ui.PendingReceiptFragment.Companion  Toast 6com.example.taobao.ui.PendingReceiptFragment.Companion  ViewModelProvider 6com.example.taobao.ui.PendingReceiptFragment.Companion  activity 6com.example.taobao.ui.PendingReceiptFragment.Companion  context 6com.example.taobao.ui.PendingReceiptFragment.Companion  	emptyList 6com.example.taobao.ui.PendingReceiptFragment.Companion  filter 6com.example.taobao.ui.PendingReceiptFragment.Companion  java 6com.example.taobao.ui.PendingReceiptFragment.Companion  let 6com.example.taobao.ui.PendingReceiptFragment.Companion  newInstance 6com.example.taobao.ui.PendingReceiptFragment.Companion  	viewModel 6com.example.taobao.ui.PendingReceiptFragment.Companion  OnButtonClickListener @com.example.taobao.ui.PendingReceiptFragment.OrderPendingAdapter  OnItemClickListener @com.example.taobao.ui.PendingReceiptFragment.OrderPendingAdapter  Map kotlin.collections  filter kotlin.collections  filter kotlin.sequences  filter kotlin.text  let android.view.View  setBackgroundResource android.view.View  setBackgroundResource android.widget.TextView  setTextColor android.widget.TextView  getColor #androidx.core.content.ContextCompat  
ContextCompat androidx.fragment.app.Fragment  listOf androidx.fragment.app.Fragment  
black_text com.example.taobao.R.color  orange_text com.example.taobao.R.color  btn_taobao_gray_bg com.example.taobao.R.drawable  btn_taobao_orange_bg com.example.taobao.R.drawable  
ContextCompat com.example.taobao.ui  
ContextCompat 'com.example.taobao.ui.AllOrdersFragment  
filterButtons 'com.example.taobao.ui.AllOrdersFragment  let 'com.example.taobao.ui.AllOrdersFragment  listOf 'com.example.taobao.ui.AllOrdersFragment  requireContext 'com.example.taobao.ui.AllOrdersFragment  setupFilterButtons 'com.example.taobao.ui.AllOrdersFragment  setupNavigation 'com.example.taobao.ui.AllOrdersFragment  updateButtonStyles 'com.example.taobao.ui.AllOrdersFragment  view 'com.example.taobao.ui.AllOrdersFragment  viewLifecycleOwner 'com.example.taobao.ui.AllOrdersFragment  	viewModel 'com.example.taobao.ui.AllOrdersFragment  navigateToEditOrder $com.example.taobao.ui.OrderViewModel  onDoneNavigating $com.example.taobao.ui.OrderViewModel  Iterator kotlin.collections  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  iterator kotlin.collections.List  ARG_SHOW_ONLY_PENDING_SHIPMENT androidx.fragment.app.Fragment  OrderShipmentAdapter androidx.fragment.app.Fragment  PendingShipmentFragment androidx.fragment.app.Fragment  OnButtonClickListener 3androidx.fragment.app.Fragment.OrderShipmentAdapter  OnItemClickListener 3androidx.fragment.app.Fragment.OrderShipmentAdapter  fragment_pending_shipment com.example.taobao.R.layout  item_order_shipment com.example.taobao.R.layout  ARG_SHOW_ONLY_PENDING_SHIPMENT com.example.taobao.ui  OrderShipmentAdapter com.example.taobao.ui  PendingShipmentFragment com.example.taobao.ui  Button *com.example.taobao.ui.OrderShipmentAdapter  	ImageView *com.example.taobao.ui.OrderShipmentAdapter  Int *com.example.taobao.ui.OrderShipmentAdapter  LayoutInflater *com.example.taobao.ui.OrderShipmentAdapter  LinearLayout *com.example.taobao.ui.OrderShipmentAdapter  List *com.example.taobao.ui.OrderShipmentAdapter  OnButtonClickListener *com.example.taobao.ui.OrderShipmentAdapter  OnItemClickListener *com.example.taobao.ui.OrderShipmentAdapter  OrderPending *com.example.taobao.ui.OrderShipmentAdapter  R *com.example.taobao.ui.OrderShipmentAdapter  RecyclerView *com.example.taobao.ui.OrderShipmentAdapter  TextView *com.example.taobao.ui.OrderShipmentAdapter  View *com.example.taobao.ui.OrderShipmentAdapter  	ViewGroup *com.example.taobao.ui.OrderShipmentAdapter  
ViewHolder *com.example.taobao.ui.OrderShipmentAdapter  buttonClickListener *com.example.taobao.ui.OrderShipmentAdapter  forEachIndexed *com.example.taobao.ui.OrderShipmentAdapter  itemClickListener *com.example.taobao.ui.OrderShipmentAdapter  notifyDataSetChanged *com.example.taobao.ui.OrderShipmentAdapter  orders *com.example.taobao.ui.OrderShipmentAdapter  replace *com.example.taobao.ui.OrderShipmentAdapter  setOnButtonClickListener *com.example.taobao.ui.OrderShipmentAdapter  setOnItemClickListener *com.example.taobao.ui.OrderShipmentAdapter  updateOrders *com.example.taobao.ui.OrderShipmentAdapter  onApplyForInvoiceClick @com.example.taobao.ui.OrderShipmentAdapter.OnButtonClickListener  onModifyAddressClick @com.example.taobao.ui.OrderShipmentAdapter.OnButtonClickListener  onUrgeShipmentClick @com.example.taobao.ui.OrderShipmentAdapter.OnButtonClickListener  onItemClick >com.example.taobao.ui.OrderShipmentAdapter.OnItemClickListener  
ViewHolder 7com.example.taobao.ui.OrderShipmentAdapter.RecyclerView  MarginLayoutParams 4com.example.taobao.ui.OrderShipmentAdapter.ViewGroup  R 5com.example.taobao.ui.OrderShipmentAdapter.ViewHolder  btnApplyForInvoice 5com.example.taobao.ui.OrderShipmentAdapter.ViewHolder  btnModifyAddress 5com.example.taobao.ui.OrderShipmentAdapter.ViewHolder  btnUrgeShipment 5com.example.taobao.ui.OrderShipmentAdapter.ViewHolder  itemView 5com.example.taobao.ui.OrderShipmentAdapter.ViewHolder  	ivProduct 5com.example.taobao.ui.OrderShipmentAdapter.ViewHolder  
tagsContainer 5com.example.taobao.ui.OrderShipmentAdapter.ViewHolder  
tvOrderStatus 5com.example.taobao.ui.OrderShipmentAdapter.ViewHolder  tvPayAmount 5com.example.taobao.ui.OrderShipmentAdapter.ViewHolder  
tvProductDesc 5com.example.taobao.ui.OrderShipmentAdapter.ViewHolder  
tvProductName 5com.example.taobao.ui.OrderShipmentAdapter.ViewHolder  tvProductPrice 5com.example.taobao.ui.OrderShipmentAdapter.ViewHolder  
tvQuantity 5com.example.taobao.ui.OrderShipmentAdapter.ViewHolder  tvShippingPromise 5com.example.taobao.ui.OrderShipmentAdapter.ViewHolder  tvShippingStatus 5com.example.taobao.ui.OrderShipmentAdapter.ViewHolder  
tvShopName 5com.example.taobao.ui.OrderShipmentAdapter.ViewHolder  ARG_SHOW_ONLY_PENDING_SHIPMENT -com.example.taobao.ui.PendingShipmentFragment  Boolean -com.example.taobao.ui.PendingShipmentFragment  Bundle -com.example.taobao.ui.PendingShipmentFragment  LayoutInflater -com.example.taobao.ui.PendingShipmentFragment  LinearLayoutManager -com.example.taobao.ui.PendingShipmentFragment  MainActivity -com.example.taobao.ui.PendingShipmentFragment  OrderPending -com.example.taobao.ui.PendingShipmentFragment  OrderShipmentAdapter -com.example.taobao.ui.PendingShipmentFragment  OrderViewModel -com.example.taobao.ui.PendingShipmentFragment  PendingShipmentFragment -com.example.taobao.ui.PendingShipmentFragment  R -com.example.taobao.ui.PendingShipmentFragment  RecyclerView -com.example.taobao.ui.PendingShipmentFragment  Toast -com.example.taobao.ui.PendingShipmentFragment  View -com.example.taobao.ui.PendingShipmentFragment  	ViewGroup -com.example.taobao.ui.PendingShipmentFragment  ViewModelProvider -com.example.taobao.ui.PendingShipmentFragment  activity -com.example.taobao.ui.PendingShipmentFragment  	arguments -com.example.taobao.ui.PendingShipmentFragment  context -com.example.taobao.ui.PendingShipmentFragment  	emptyList -com.example.taobao.ui.PendingShipmentFragment  filter -com.example.taobao.ui.PendingShipmentFragment  java -com.example.taobao.ui.PendingShipmentFragment  let -com.example.taobao.ui.PendingShipmentFragment  orderAdapter -com.example.taobao.ui.PendingShipmentFragment  requireActivity -com.example.taobao.ui.PendingShipmentFragment  showOnlyPendingShipment -com.example.taobao.ui.PendingShipmentFragment  viewLifecycleOwner -com.example.taobao.ui.PendingShipmentFragment  	viewModel -com.example.taobao.ui.PendingShipmentFragment  ARG_SHOW_ONLY_PENDING_SHIPMENT 7com.example.taobao.ui.PendingShipmentFragment.Companion  Bundle 7com.example.taobao.ui.PendingShipmentFragment.Companion  LinearLayoutManager 7com.example.taobao.ui.PendingShipmentFragment.Companion  OrderShipmentAdapter 7com.example.taobao.ui.PendingShipmentFragment.Companion  OrderViewModel 7com.example.taobao.ui.PendingShipmentFragment.Companion  PendingShipmentFragment 7com.example.taobao.ui.PendingShipmentFragment.Companion  R 7com.example.taobao.ui.PendingShipmentFragment.Companion  Toast 7com.example.taobao.ui.PendingShipmentFragment.Companion  ViewModelProvider 7com.example.taobao.ui.PendingShipmentFragment.Companion  activity 7com.example.taobao.ui.PendingShipmentFragment.Companion  context 7com.example.taobao.ui.PendingShipmentFragment.Companion  	emptyList 7com.example.taobao.ui.PendingShipmentFragment.Companion  filter 7com.example.taobao.ui.PendingShipmentFragment.Companion  java 7com.example.taobao.ui.PendingShipmentFragment.Companion  let 7com.example.taobao.ui.PendingShipmentFragment.Companion  	viewModel 7com.example.taobao.ui.PendingShipmentFragment.Companion  OnButtonClickListener Bcom.example.taobao.ui.PendingShipmentFragment.OrderShipmentAdapter  OnItemClickListener Bcom.example.taobao.ui.PendingShipmentFragment.OrderShipmentAdapter  RadioButton android.widget  
RadioGroup android.widget  	isChecked android.widget.CompoundButton  	isChecked android.widget.RadioButton  OnCheckedChangeListener android.widget.RadioGroup  setOnCheckedChangeListener android.widget.RadioGroup  <SAM-CONSTRUCTOR> 1android.widget.RadioGroup.OnCheckedChangeListener  isEmpty androidx.fragment.app.Fragment  rb_pending_receipt com.example.taobao.R.id  rb_pending_shipment com.example.taobao.R.id  rbPendingShipment 6com.example.taobao.databinding.FragmentAddOrderBinding  rgOrderType 6com.example.taobao.databinding.FragmentAddOrderBinding  tvAddOrderTitle 6com.example.taobao.databinding.FragmentAddOrderBinding  isEmpty com.example.taobao.ui  isEmpty &com.example.taobao.ui.AddOrderFragment  PendingShipmentFragment 'com.example.taobao.ui.AllOrdersFragment  	Companion -com.example.taobao.ui.PendingShipmentFragment  newInstance -com.example.taobao.ui.PendingShipmentFragment  newInstance 7com.example.taobao.ui.PendingShipmentFragment.Companion  isEmpty 	java.util  text android.widget.Button  
tvOrderStatus 7com.example.taobao.databinding.ItemOrderEditableBinding  rbPendingReceipt 6com.example.taobao.databinding.FragmentAddOrderBinding  
isNullOrEmpty androidx.fragment.app.Fragment  android 1androidx.recyclerview.widget.RecyclerView.Adapter  
isNullOrEmpty 1androidx.recyclerview.widget.RecyclerView.Adapter  
productImgUri #com.example.taobao.data.OrderEntity  android com.example.taobao.ui  
isNullOrEmpty &com.example.taobao.ui.AddOrderFragment  
productImgUri "com.example.taobao.ui.OrderPending  android )com.example.taobao.ui.OrderPendingAdapter  
isNullOrEmpty )com.example.taobao.ui.OrderPendingAdapter  android *com.example.taobao.ui.OrderShipmentAdapter  
isNullOrEmpty *com.example.taobao.ui.OrderShipmentAdapter  
isNullOrEmpty 	java.util  not kotlin.Boolean  
isNullOrEmpty 
kotlin.String  android androidx.fragment.app.Fragment  android )com.example.taobao.ui.OrderDetailFragment  
isNullOrEmpty )com.example.taobao.ui.OrderDetailFragment  android 3com.example.taobao.ui.OrderDetailFragment.Companion  
isNullOrEmpty 3com.example.taobao.ui.OrderDetailFragment.Companion  tv_product_desc_detail com.example.taobao.R.id  tv_product_tags_detail com.example.taobao.R.id  joinToString )com.example.taobao.ui.OrderDetailFragment  joinToString 3com.example.taobao.ui.OrderDetailFragment.Companion  WRAP_CONTENT #android.view.ViewGroup.LayoutParams  LayoutParams android.widget.LinearLayout  context android.widget.LinearLayout  WRAP_CONTENT (android.widget.LinearLayout.LayoutParams  marginStart (android.widget.LinearLayout.LayoutParams  setTextAppearance android.widget.TextView  LinearLayout androidx.fragment.app.Fragment  forEachIndexed androidx.fragment.app.Fragment  tags_container_detail com.example.taobao.R.id  	TaobaoTag com.example.taobao.R.style  LinearLayout )com.example.taobao.ui.OrderDetailFragment  forEachIndexed )com.example.taobao.ui.OrderDetailFragment  LinearLayout 3com.example.taobao.ui.OrderDetailFragment.Companion  TextView 3com.example.taobao.ui.OrderDetailFragment.Companion  forEachIndexed 3com.example.taobao.ui.OrderDetailFragment.Companion  presaleInfo #com.example.taobao.data.OrderEntity  
etPresaleInfo 6com.example.taobao.databinding.FragmentAddOrderBinding  presaleInfo "com.example.taobao.ui.OrderPending  et_order_services com.example.taobao.R.id  et_presale_info com.example.taobao.R.id  et_tmall_points com.example.taobao.R.id  et_transaction_snapshot com.example.taobao.R.id  etOrderServices )com.example.taobao.ui.OrderDetailFragment  
etPresaleInfo )com.example.taobao.ui.OrderDetailFragment  
etTmallPoints )com.example.taobao.ui.OrderDetailFragment  etTransactionSnapshot )com.example.taobao.ui.OrderDetailFragment  onResume androidx.fragment.app.Fragment  clearEditingOrder $com.example.taobao.ui.OrderViewModel  clearNavigationFlags $com.example.taobao.ui.OrderViewModel  
isNotEmpty androidx.fragment.app.Fragment  replace androidx.fragment.app.Fragment  
startsWith androidx.fragment.app.Fragment  
isNotEmpty com.example.taobao.ui  
startsWith com.example.taobao.ui  formatPrice &com.example.taobao.ui.AddOrderFragment  
isNotEmpty &com.example.taobao.ui.AddOrderFragment  replace &com.example.taobao.ui.AddOrderFragment  
startsWith &com.example.taobao.ui.AddOrderFragment  formatPrice )com.example.taobao.ui.OrderDetailFragment  
isNotEmpty )com.example.taobao.ui.OrderDetailFragment  replace )com.example.taobao.ui.OrderDetailFragment  
startsWith )com.example.taobao.ui.OrderDetailFragment  trim )com.example.taobao.ui.OrderDetailFragment  
isNotEmpty 3com.example.taobao.ui.OrderDetailFragment.Companion  replace 3com.example.taobao.ui.OrderDetailFragment.Companion  
startsWith 3com.example.taobao.ui.OrderDetailFragment.Companion  trim 3com.example.taobao.ui.OrderDetailFragment.Companion  String 	java.util  
isNotEmpty 	java.util  replace 	java.util  
startsWith 	java.util  
isNotEmpty 
kotlin.String  
startsWith 
kotlin.String  
isNotEmpty kotlin.collections  
startsWith 	kotlin.io  
isNotEmpty kotlin.text  
startsWith kotlin.text  getOrCreateAddOrderFragment 'com.example.taobao.ui.AllOrdersFragment  Intent android.content  takePersistableUriPermission android.content.ContentResolver  contentResolver android.content.Context  FLAG_GRANT_READ_URI_PERMISSION android.content.Intent  	Exception androidx.fragment.app.Fragment  Intent androidx.fragment.app.Fragment  SecurityException androidx.fragment.app.Fragment  ic_add_photo com.example.taobao.R.drawable  	Exception com.example.taobao.ui  Intent com.example.taobao.ui  SecurityException com.example.taobao.ui  Intent &com.example.taobao.ui.AddOrderFragment  requireContext &com.example.taobao.ui.AddOrderFragment  	Exception )com.example.taobao.ui.OrderDetailFragment  	Exception 	java.lang  SecurityException 	java.lang  	Exception 	java.util  Intent 	java.util  SecurityException 	java.util  
visibility android.widget.Button  View 1androidx.recyclerview.widget.RecyclerView.Adapter  LinearLayout 1androidx.recyclerview.widget.RecyclerView.Adapter                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               