package com.example.taobao.ui

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.core.net.toUri
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import com.example.taobao.R

class OrderDetailFragment : Fragment() {

    private val viewModel: OrderViewModel by activityViewModels()
    private var orderId: String? = null
    private lateinit var currentOrder: OrderPending

    private lateinit var etOrderStatusText: EditText
    private lateinit var etBuyerAddress: EditText
    private lateinit var etBuyerName: EditText
    private lateinit var etStoreName: EditText
    private lateinit var etStoreRating: EditText
    private lateinit var etProductName: EditText
    private lateinit var etProductPrice: EditText
    private lateinit var etQuantity: EditText
    private lateinit var etOrderNumber: EditText
    private lateinit var etCreationTime: EditText
    private lateinit var etPaymentTime: EditText
    private lateinit var etWechatTransactionId: EditText
    private lateinit var etPresaleInfo: EditText
    private lateinit var etTransactionSnapshot: EditText
    private lateinit var etTmallPoints: EditText
    private lateinit var etOrderServices: EditText
    private lateinit var ivProductImage: ImageView
    private lateinit var tvTotalPayment: TextView
    private lateinit var tvOrderServices: TextView
    private lateinit var btnCopyOrderNumber: Button


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            orderId = it.getString(ARG_ORDER_ID)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_order_detail, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        orderId?.let {
            viewModel.loadOrderDetails(it)
        }

        bindViews(view)

        viewModel.selectedOrderDetails.observe(viewLifecycleOwner) { order ->
            order?.let {
                currentOrder = it
                populateUi(it)
            }
        }

        view.findViewById<ImageView>(R.id.iv_back).setOnClickListener {
            parentFragmentManager.popBackStack()
        }

        btnCopyOrderNumber.setOnClickListener {
            val clipboard = requireActivity().getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clip = ClipData.newPlainText("Order Number", etOrderNumber.text.toString())
            clipboard.setPrimaryClip(clip)
            Toast.makeText(context, "Order number copied", Toast.LENGTH_SHORT).show()
        }

        // The save button was removed from the layout, so the click listener is commented out.
        // view.findViewById<Button>(R.id.btn_save_changes).setOnClickListener {
        //     saveChanges()
        // }
    }

    private fun bindViews(view: View) {
        etOrderStatusText = view.findViewById(R.id.et_order_status_text)
        etBuyerAddress = view.findViewById(R.id.et_buyer_address)
        etBuyerName = view.findViewById(R.id.et_buyer_name)
        etStoreName = view.findViewById(R.id.et_store_name_detail)
        etStoreRating = view.findViewById(R.id.et_store_rating)
        etProductName = view.findViewById(R.id.et_product_name_detail)
        etProductPrice = view.findViewById(R.id.et_product_price_detail)
        etQuantity = view.findViewById(R.id.et_quantity_detail)
        etOrderNumber = view.findViewById(R.id.et_order_number)
        etCreationTime = view.findViewById(R.id.et_creation_time)
        etPaymentTime = view.findViewById(R.id.et_payment_time)
        etWechatTransactionId = view.findViewById(R.id.et_wechat_transaction_id)
        etPresaleInfo = view.findViewById(R.id.et_presale_info)
        etTransactionSnapshot = view.findViewById(R.id.et_transaction_snapshot)
        etTmallPoints = view.findViewById(R.id.et_tmall_points)
        etOrderServices = view.findViewById(R.id.et_order_services)
        ivProductImage = view.findViewById(R.id.iv_product_image_detail)
        tvTotalPayment = view.findViewById(R.id.tv_total_payment)
        tvOrderServices = view.findViewById(R.id.tv_order_services)
        btnCopyOrderNumber = view.findViewById(R.id.btn_copy_order_number)
    }

    private fun populateUi(order: OrderPending) {
        etOrderStatusText.setText(order.orderStatusText)
        etBuyerAddress.setText(order.buyerAddress)
        etBuyerName.setText(order.buyerName)
        etStoreName.setText(order.shopName)
        if (order.shopIconUri != null) {
            try {
                view?.findViewById<ImageView>(R.id.iv_store_icon_detail)?.setImageURI(order.shopIconUri.toUri())
            } catch (e: Exception) {
                // If URI is invalid, fall back to default image
                view?.findViewById<ImageView>(R.id.iv_store_icon_detail)?.setImageResource(R.drawable.ic_launcher_background)
            }
        } else {
            // Set a default or placeholder image if no URI is present
            view?.findViewById<ImageView>(R.id.iv_store_icon_detail)?.setImageResource(R.drawable.ic_launcher_background)
        }
        etStoreRating.setText(order.storeRating)
        etProductName.setText(order.productName)
        etProductPrice.setText(order.productPrice)
        etQuantity.setText(order.quantity)
        etOrderNumber.setText(order.orderNumber)
        etCreationTime.setText(order.creationTime)
        etPaymentTime.setText(order.paymentTime)
        etWechatTransactionId.setText(order.wechatTransactionId)
        etPresaleInfo.setText(order.presaleInfo)
        etTransactionSnapshot.setText(order.transactionSnapshotText)
        etTmallPoints.setText(order.tmallPoints)
        etOrderServices.setText(order.orderServices)
        if (!order.productImgUri.isNullOrEmpty()) {
            try {
                ivProductImage.setImageURI(android.net.Uri.parse(order.productImgUri))
            } catch (e: Exception) {
                // If URI is invalid, fall back to default image
                ivProductImage.setImageResource(order.productImg)
            }
        } else {
            ivProductImage.setImageResource(order.productImg)
        }
        tvTotalPayment.text = order.payInfo
        // 动态添加标签
        val tagsContainer = view?.findViewById<LinearLayout>(R.id.tags_container_detail)
        tagsContainer?.removeAllViews()
        val context = tagsContainer?.context
        order.tags.forEachIndexed { index, tagText ->
            val tagView = TextView(context)
            tagView.text = tagText
            tagView.setTextAppearance(R.style.TaobaoTag)
            if (index > 0) {
                val params = LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT)
                params.marginStart = 12
                tagView.layoutParams = params
            }
            tagsContainer?.addView(tagView)
        }
        view?.findViewById<TextView>(R.id.tv_product_desc_detail)?.text = order.productDesc
    }

    private fun formatPrice(priceInput: String): String {
        val cleanPrice = priceInput.trim()
        return if (cleanPrice.isNotEmpty()) {
            // If it already starts with ¥, keep it as is
            if (cleanPrice.startsWith("¥")) {
                cleanPrice
            } else {
                // If it's just numbers, add ¥ prefix
                "¥$cleanPrice"
            }
        } else {
            cleanPrice
        }
    }

    private fun saveChanges() {
        val updatedOrder = currentOrder.copy(
            orderStatusText = etOrderStatusText.text.toString(),
            buyerAddress = etBuyerAddress.text.toString(),
            buyerName = etBuyerName.text.toString(),
            shopName = etStoreName.text.toString(),
            storeRating = etStoreRating.text.toString(),
            productName = etProductName.text.toString(),
            productPrice = formatPrice(etProductPrice.text.toString()),
            quantity = etQuantity.text.toString(),
            orderNumber = etOrderNumber.text.toString(),
            creationTime = etCreationTime.text.toString(),
            paymentTime = etPaymentTime.text.toString(),
            wechatTransactionId = etWechatTransactionId.text.toString(),
            presaleInfo = etPresaleInfo.text.toString(),
            transactionSnapshotText = etTransactionSnapshot.text.toString(),
            tmallPoints = etTmallPoints.text.toString(),
            orderServices = etOrderServices.text.toString(),
            payInfo = tvTotalPayment.text.toString()
        )
        viewModel.updateOrder(updatedOrder)
        Toast.makeText(requireContext(), "Changes Saved", Toast.LENGTH_SHORT).show()
        parentFragmentManager.popBackStack()
    }

    companion object {
        private const val ARG_ORDER_ID = "order_id"

        fun newInstance(orderId: String): OrderDetailFragment {
            val fragment = OrderDetailFragment()
            val args = Bundle()
            args.putString(ARG_ORDER_ID, orderId)
            fragment.arguments = args
            return fragment
        }
    }
} 