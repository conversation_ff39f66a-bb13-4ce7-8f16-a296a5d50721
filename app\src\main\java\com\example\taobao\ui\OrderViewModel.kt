package com.example.taobao.ui

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.example.taobao.R
import com.example.taobao.data.AppDatabase
import com.example.taobao.data.OrderRepository
import kotlinx.coroutines.launch
import java.util.UUID

// Add a unique ID to the data class to help with editing and deleting
data class OrderPending(
    val id: String = UUID.randomUUID().toString(),
    val shopName: String,
    val shopIconUri: String?, // Add shop icon URI
    val orderStatus: String,
    val productImg: Int,
    val productImgUri: String?, // 新增字段，商品图片uri
    val productName: String,
    val productPrice: String,
    val productDesc: String,
    val quantity: String,
    val shippingStatus: String,
    val shippingPromise: String,
    val tags: List<String>,
    val logistics: String,
    val payInfo: String,

    // New fields for Order Detail
    val buyerAddress: String? = "碧桂园·海昌天澜148幢",
    val buyerName: String? = "淘宝用户 86-152****6073",
    val orderStatusText: String? = "买家已付款",
    val storeRating: String? = "88VIP好评率99%, 平均3天内发货",
    val orderNumber: String? = "4610644706593328843",
    val transactionSnapshotText: String? = "发生交易争议时,可作为判断依据",
    val tmallPoints: String? = "获得25点积分",
    val wechatTransactionId: String? = "4349502572202507022480548733",
    val creationTime: String? = "2025-07-02 16:33:07",
    val paymentTime: String? = "2025-07-02 16:33:14",
    val orderServices: String? = "包含假一赔四等服务",
    val presaleInfo: String = ""
)

class OrderViewModel(application: Application) : AndroidViewModel(application) {

    private val repository: OrderRepository
    val orders: LiveData<List<OrderPending>>

    private val _navigateToPendingReceipt = MutableLiveData<Boolean>()
    val navigateToPendingReceipt: LiveData<Boolean> = _navigateToPendingReceipt

    private val _selectedOrderForEdit = MutableLiveData<OrderPending?>()
    val selectedOrderForEdit: LiveData<OrderPending?> = _selectedOrderForEdit

    private val _navigateToEditOrder = MutableLiveData<Boolean>()
    val navigateToEditOrder: LiveData<Boolean> = _navigateToEditOrder

    private val _selectedOrderDetails = MutableLiveData<OrderPending?>()
    val selectedOrderDetails: LiveData<OrderPending?> = _selectedOrderDetails

    init {
        val orderDao = AppDatabase.getDatabase(application).orderDao()
        repository = OrderRepository(orderDao)
        orders = repository.allOrders.asLiveData()
        
        // 添加初始订单（仅在首次运行时）
        viewModelScope.launch {
            if (orders.value.isNullOrEmpty()) {
                repository.insertOrder(getInitialOrder())
            }
        }
    }

    fun loadOrderDetails(orderId: String) {
        viewModelScope.launch {
            repository.getOrderById(orderId).collect { order ->
                _selectedOrderDetails.postValue(order)
            }
        }
    }

    fun addOrder(order: OrderPending) {
        viewModelScope.launch {
            repository.insertOrder(order)
            _navigateToPendingReceipt.value = true
        }
    }

    fun updateOrder(updatedOrder: OrderPending) {
        viewModelScope.launch {
            repository.updateOrder(updatedOrder)
            _navigateToPendingReceipt.value = true
        }
    }

    fun deleteOrder(order: OrderPending) {
        viewModelScope.launch {
            repository.deleteOrder(order)
        }
    }

    fun onOrderSelectedForEdit(order: OrderPending) {
        _selectedOrderForEdit.value = order
        _navigateToEditOrder.value = true
    }

    fun onDoneNavigating() {
        _navigateToPendingReceipt.value = false
        _navigateToEditOrder.value = false
        _selectedOrderForEdit.value = null
    }

    fun clearNavigationFlags() {
        _navigateToPendingReceipt.value = false
        _navigateToEditOrder.value = false
    }

    fun clearEditingOrder() {
        _selectedOrderForEdit.value = null
    }

    private fun getInitialOrder(): OrderPending {
        return OrderPending(
            shopName = "Apple Store 官方旗舰店",
            shopIconUri = null, // No initial icon for the default order
            orderStatus = "卖家已发货",
            productImg = R.drawable.test,
            productImgUri = null,
            productName = "Apple/苹果 iPhone 12 Pro ...",
            productPrice = "¥10099.00",
            productDesc = "海军蓝, 256G, 6G内存",
            quantity = "x1",
            shippingStatus = "运输中",
            shippingPromise = "你的宝贝将由邮政速递护送",
            tags = listOf("天猫无忧购", "七天无理由退换"),
            logistics = "运输中 你的宝贝将由邮政速递护送",
            payInfo = "¥10,099.00",
            // Add initial values for new fields
            buyerAddress = "碧桂园·海昌天澜148幢",
            buyerName = "淘宝用户 86-152****6073",
            orderStatusText = "买家已付款",
            storeRating = "88VIP好评率99%, 平均3天内发货",
            orderNumber = "4610644706593328843",
            transactionSnapshotText = "发生交易争议时,可作为判断依据",
            tmallPoints = "获得25点积分",
            wechatTransactionId = "4349502572202507022480548733",
            creationTime = "2025-07-02 16:33:07",
            paymentTime = "2025-07-02 16:33:14",
            orderServices = "包含假一赔四等服务"
        )
    }
} 