<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_add_order_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="添加订单"
            android:textSize="24sp"
            android:textColor="@color/black_text"
            android:layout_marginBottom="16dp"
            android:layout_gravity="center_horizontal"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="订单类型"
            android:textSize="18sp"
            android:textColor="@color/black_text"
            android:layout_marginBottom="8dp"/>

        <RadioGroup
            android:id="@+id/rg_order_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <RadioButton
                android:id="@+id/rb_pending_shipment"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="待发货"
                android:checked="true"/>

            <RadioButton
                android:id="@+id/rb_pending_receipt"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="待收货"/>
        </RadioGroup>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="店铺信息"
            android:textSize="18sp"
            android:textColor="@color/black_text"
            android:layout_marginBottom="8dp"/>

        <ImageView
            android:id="@+id/iv_shop_icon"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:src="@drawable/ic_add_photo"
            android:scaleType="centerCrop"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="8dp"/>

        <Button
            android:id="@+id/btn_upload_shop_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="上传店铺头像"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="16dp"/>

        <EditText android:id="@+id/et_shop_name" style="@style/AddOrderEditText" android:hint="店铺名称" />
        <EditText android:id="@+id/et_order_status" style="@style/AddOrderEditText" android:hint="订单状态 (例如: 卖家已发货)" />
        <EditText android:id="@+id/et_product_name" style="@style/AddOrderEditText" android:hint="商品名称" />
        <EditText android:id="@+id/et_product_price" style="@style/AddOrderEditText" android:hint="商品价格 (例如: 10099.00)" android:inputType="numberDecimal" />
        <EditText android:id="@+id/et_product_desc" style="@style/AddOrderEditText" android:hint="商品描述 (例如: 海军蓝, 256G)" />
        <EditText android:id="@+id/et_quantity" style="@style/AddOrderEditText" android:hint="数量 (例如: x1)" />
        <EditText android:id="@+id/et_shipping_status" style="@style/AddOrderEditText" android:hint="物流状态 (例如: 运输中)" />
        <EditText android:id="@+id/et_shipping_promise" style="@style/AddOrderEditText" android:hint="物流承诺 (例如: 你的宝贝将由邮政速递护送)" />
        <EditText android:id="@+id/et_tags" style="@style/AddOrderEditText" android:hint="标签 (用逗号分隔, 例如: 天猫无忧购,七天包退)" />
        <EditText android:id="@+id/et_logistics" style="@style/AddOrderEditText" android:hint="物流信息" />
        <EditText android:id="@+id/et_pay_info" style="@style/AddOrderEditText" android:hint="实付款 (例如: 10099.00)" android:inputType="numberDecimal" />
        <EditText android:id="@+id/et_buyer_address" style="@style/AddOrderEditText" android:hint="买家地址" />
        <EditText android:id="@+id/et_buyer_name" style="@style/AddOrderEditText" android:hint="买家用户名" />
        <EditText android:id="@+id/et_order_status_text" style="@style/AddOrderEditText" android:hint="订单状态文本 (例如: 买家已付款)" />
        <EditText android:id="@+id/et_store_rating" style="@style/AddOrderEditText" android:hint="店铺评分" />
        <EditText android:id="@+id/et_order_number" style="@style/AddOrderEditText" android:hint="订单编号" />
        <EditText android:id="@+id/et_transaction_snapshot" style="@style/AddOrderEditText" android:hint="交易快照文本" />
        <EditText android:id="@+id/et_tmall_points" style="@style/AddOrderEditText" android:hint="天猫积分" />
        <EditText android:id="@+id/et_wechat_transaction_id" style="@style/AddOrderEditText" android:hint="微信交易号" />
        <EditText android:id="@+id/et_creation_time" style="@style/AddOrderEditText" android:hint="创建时间" />
        <EditText android:id="@+id/et_payment_time" style="@style/AddOrderEditText" android:hint="付款时间" />
        <EditText android:id="@+id/et_order_services" style="@style/AddOrderEditText" android:hint="订单服务" />
        <EditText
            android:id="@+id/et_presale_info"
            style="@style/AddOrderEditText"
            android:hint="预售信息（如：10月30日前发货）" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="?android:attr/listDivider"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="商品信息"
            android:textSize="18sp"
            android:textColor="@color/black_text"
            android:layout_marginBottom="8dp"/>

        <ImageView
            android:id="@+id/iv_product_image"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:src="@drawable/ic_add_photo"
            android:scaleType="centerCrop"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="8dp"/>

        <Button
            android:id="@+id/btn_upload_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="上传商品图片"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="16dp"/>

        <Button
            android:id="@+id/btn_save_order"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="保存订单"
            style="@style/OrangeButton"
            android:layout_marginTop="16dp"/>
    </LinearLayout>
</ScrollView> 