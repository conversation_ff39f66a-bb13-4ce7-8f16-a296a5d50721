<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="TabTextStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:gravity">center</item>
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
        <item name="android:textColor">@color/black_text</item>
        <item name="android:textSize">14sp</item>
        <item name="android:background">@drawable/tab_background_selector</item>
    </style>

    <style name="GrayButton">
        <item name="android:background">@drawable/btn_gray_bg</item>
        <item name="android:textColor">@color/gray_text</item>
        <item name="android:textSize">12sp</item>
        <item name="android:paddingStart">10dp</item>
        <item name="android:paddingEnd">10dp</item>
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
    </style>

    <style name="OrangeButton">
        <item name="android:background">@drawable/btn_orange_bg</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">12sp</item>
        <item name="android:paddingStart">10dp</item>
        <item name="android:paddingEnd">10dp</item>
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
    </style>

    <style name="RedBorderButton">
        <item name="android:background">@drawable/btn_red_border_bg</item>
        <item name="android:textColor">#FF0000</item>
        <item name="android:textSize">12sp</item>
        <item name="android:paddingStart">10dp</item>
        <item name="android:paddingEnd">10dp</item>
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
    </style>

    <style name="AddOrderEditText">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginBottom">8dp</item>
        <item name="android:background">@drawable/edit_text_bg</item>
        <item name="android:padding">12dp</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="TaobaoGrayButton" parent="ActionButton">
        <item name="android:background">@drawable/btn_taobao_gray_bg</item>
        <item name="android:textColor">@color/gray_text</item>
        <item name="android:textSize">12sp</item>
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
        <item name="android:layout_gravity">center</item>
    </style>

    <style name="TaobaoOrangeButton" parent="ActionButton">
        <item name="android:background">@drawable/btn_taobao_orange_bg</item>
        <item name="android:textColor">#D85E00</item>
        <item name="android:textSize">12sp</item>
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
        <item name="android:layout_gravity">center</item>
    </style>

    <style name="TaobaoFilterButton">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">30dp</item>
        <item name="android:background">@drawable/btn_gray_bg</item>
        <item name="android:textColor">@color/black_text</item>
        <item name="android:textSize">13sp</item>
        <item name="android:paddingStart">14dp</item>
        <item name="android:paddingEnd">14dp</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="TaobaoFilterButton.Selected">
        <item name="android:background">@drawable/btn_filter_selected_bg</item>
        <item name="android:textColor">@color/taobao_orange</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="TaobaoTag">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">#FF5500</item>
        <item name="android:textSize">13sp</item>
    </style>

    <style name="ActionButton">
        <item name="android:layout_height">32dp</item>
        <item name="android:textSize">12sp</item>
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:gravity">center</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="android:elevation">0dp</item>
    </style>

    <style name="ActionButton.Gray" parent="ActionButton">
        <item name="android:background">@drawable/btn_gray_bg</item>
        <item name="android:textColor">@color/gray_text</item>
        <item name="android:textSize">12sp</item>
        <item name="android:paddingStart">10dp</item>
        <item name="android:paddingEnd">10dp</item>
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
    </style>

    <style name="ActionButton.Orange" parent="ActionButton">
        <item name="android:background">@drawable/btn_orange_bg</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">12sp</item>
        <item name="android:paddingStart">10dp</item>
        <item name="android:paddingEnd">10dp</item>
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
    </style>

    <style name="ActionButton.RedBorder" parent="ActionButton">
        <item name="android:background">@drawable/btn_red_border_bg</item>
        <item name="android:textColor">#FF0000</item>
        <item name="android:textSize">12sp</item>
        <item name="android:paddingStart">10dp</item>
        <item name="android:paddingEnd">10dp</item>
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
    </style>
</resources> 