package com.example.taobao.data

import com.example.taobao.ui.OrderPending
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

/**
 * 订单仓库类，作为ViewModel和数据库之间的中间层
 */
class OrderRepository(private val orderDao: OrderDao) {

    /**
     * 获取所有订单，并将数据库实体转换为UI层使用的模型
     */
    val allOrders: Flow<List<OrderPending>> = orderDao.getAllOrders().map { entities ->
        entities.map { it.toOrderPending() }
    }

    /**
     * 根据ID获取一个订单
     */
    fun getOrderById(orderId: String): Flow<OrderPending?> {
        return orderDao.getOrderById(orderId).map { entity ->
            entity?.toOrderPending()
        }
    }

    /**
     * 插入一个新订单
     */
    suspend fun insertOrder(order: OrderPending) {
        orderDao.insertOrder(OrderEntity.fromOrderPending(order))
    }

    /**
     * 插入多个订单
     */
    suspend fun insertOrders(orders: List<OrderPending>) {
        val entities = orders.map { OrderEntity.fromOrderPending(it) }
        orderDao.insertOrders(entities)
    }

    /**
     * 更新一个订单
     */
    suspend fun updateOrder(order: OrderPending) {
        orderDao.updateOrder(OrderEntity.fromOrderPending(order))
    }

    /**
     * 删除一个订单
     */
    suspend fun deleteOrder(order: OrderPending) {
        orderDao.deleteOrder(OrderEntity.fromOrderPending(order))
    }

    /**
     * 根据ID删除一个订单
     */
    suspend fun deleteOrderById(orderId: String) {
        orderDao.deleteOrderById(orderId)
    }

    /**
     * 删除所有订单
     */
    suspend fun deleteAllOrders() {
        orderDao.deleteAllOrders()
    }
} 