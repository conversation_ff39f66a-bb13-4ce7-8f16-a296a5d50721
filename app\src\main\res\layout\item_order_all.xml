<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="0dp"
    app:cardElevation="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- 店铺信息 -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="10dp">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_store_gray_18dp"
                    app:tint="@color/gray_text" />

                <TextView
                    android:id="@+id/tvShopName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="Apple Store 官方旗舰店"
                    android:textColor="@color/black_text"
                    android:textSize="15sp" />
            </LinearLayout>

            <TextView
                android:id="@+id/tvOrderStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:text="卖家已发货"
                android:textColor="@color/orange_deep"
                android:textSize="14sp" />
        </RelativeLayout>

        <!-- 商品信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingTop="12dp"
            android:paddingBottom="12dp">

            <ImageView
                android:id="@+id/ivProduct"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:scaleType="centerCrop"
                android:src="@drawable/test" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvProductName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:text="Apple/苹果 iPhone 16 Pro ... ¥13999.00"
                    android:textColor="@color/black_text"
                    android:textSize="15sp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvProductDesc"
                        android.layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="海军蓝，256G，6G内存"
                        android:textColor="@color/gray_text"
                        android:textSize="13sp" />

                    <TextView
                        android:id="@+id/tvQuantity"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:text="x1"
                        android:textColor="@color/gray_text"
                        android:textSize="13sp" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvTag"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:background="@drawable/tag_bg"
                    android:paddingStart="4dp"
                    android:paddingEnd="4dp"
                    android:paddingTop="2dp"
                    android:paddingBottom="2dp"
                    android:text="七天无理由退换"
                    android:textColor="#FF5500"
                    android:textSize="10sp" />
            </LinearLayout>
        </LinearLayout>

        <!-- 物流信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:background="#F9F9F9"
            android:paddingStart="8dp"
            android:paddingEnd="8dp">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_logistics_truck_gray_18dp"
                app:tint="@color/gray_text" />

            <TextView
                android:id="@+id/tvLogistics"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:text="运输中 预计明天送达"
                android:textColor="@color/black_text"
                android:textSize="13sp" />
        </LinearLayout>

        <!-- 支付信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical|end"
            android:orientation="horizontal"
            android:paddingTop="12dp">

            <TextView
                android:id="@+id/tvPay"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="实付款 ¥13,999.00"
                android:textColor="@color/black_text"
                android:textSize="14sp" />
        </LinearLayout>

        <!-- 按钮区域 -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="12dp">

            <TextView
                android:id="@+id/btnMore"
                android:layout_width="wrap_content"
                android:layout_height="32dp"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:background="@android:color/transparent"
                android:gravity="center"
                android:text="更多"
                android:textColor="@color/gray_text"
                android:textSize="14sp" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btnModifyAddress"
                    style="@style/TaobaoGrayButton"
                    android:layout_width="wrap_content"
                    android:layout_height="32dp"
                    android:text="修改地址" />

                <Button
                    android:id="@+id/btnCheckLogistics"
                    style="@style/TaobaoGrayButton"
                    android:layout_width="wrap_content"
                    android:layout_height="32dp"
                    android:layout_marginStart="8dp"
                    android:text="查看物流" />

                <Button
                    android:id="@+id/btnConfirmReceipt"
                    style="@style/TaobaoOrangeButton"
                    android:layout_width="wrap_content"
                    android:layout_height="32dp"
                    android:layout_marginStart="8dp"
                    android:text="确认收货" />
            </LinearLayout>
        </RelativeLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView> 