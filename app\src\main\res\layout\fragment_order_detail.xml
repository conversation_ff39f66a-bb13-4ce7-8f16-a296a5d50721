<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    tools:context=".ui.OrderDetailFragment">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/bottom_bar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Top Bar -->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp">

                <ImageView
                    android:id="@+id/iv_back"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_centerVertical="true"
                    android:src="@drawable/ic_arrow_back" />

                <EditText
                    android:id="@+id/et_order_status_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:background="@android:color/transparent"
                    android:textColor="@android:color/black"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    tools:text="买家已付款" />

                <ImageView
                    android:id="@+id/iv_more"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:src="@drawable/ic_more_horiz" />
            </RelativeLayout>

            <!-- Address Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingBottom="16dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/locationlogo" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <EditText
                        android:id="@+id/et_buyer_address"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        android:textColor="@android:color/black"
                        android:textSize="16sp"
                        tools:text="碧桂园·海昌天澜148幢" />

                    <EditText
                        android:id="@+id/et_buyer_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        android:textColor="@android:color/darker_gray"
                        android:textSize="12sp"
                        tools:text="淘宝用户 86-152****6073" />
                </LinearLayout>

                <!-- <Button
                    android:id="@+id/btn_modify_address_top"
                    style="?android:attr/borderlessButtonStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="修改 >"
                    android:textColor="@color/orange_text"
                    android:textSize="14sp" /> -->
            </LinearLayout>

            <!-- Presale Section -->
            <View
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:background="#F0F0F0"
                android:layout_marginTop="0dp"
                android:layout_marginBottom="0dp" />
            <LinearLayout
                android:id="@+id/layout_presale"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:layout_marginTop="0dp"
                android:layout_marginBottom="0dp"
                android:paddingTop="0dp"
                android:paddingBottom="0dp">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/greenshandian" />

                <EditText
                    android:id="@+id/et_presale_info"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:background="@android:color/transparent"
                    android:textColor="@android:color/black"
                    android:textColorHint="@android:color/black"
                    android:textSize="15sp"
                    android:hint="预售，10月30日16:34前发货"
                    android:paddingTop="0dp"
                    android:paddingBottom="0dp"
                    tools:text="预售，10月30日16:34前发货" />
                <TextView
                    android:id="@+id/tv_presale_arrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=">"
                    android:textColor="#B0B0B0"
                    android:textSize="18sp"
                    android:layout_marginStart="8dp"
                    android:gravity="center_vertical" />
            </LinearLayout>
            <View
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:background="#F0F0F0"
                android:layout_marginTop="0dp"
                android:layout_marginBottom="0dp" />

            <!-- Store & Product Section in a CardView -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="0dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">
                    <!-- Store Section -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/iv_store_icon_detail"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:src="@drawable/ic_launcher_background" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <EditText
                                android:id="@+id/et_store_name_detail"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@android:color/transparent"
                                android:textColor="@android:color/black"
                                android:textSize="16sp"
                                tools:text="原神旗舰店" />

                            <EditText
                                android:id="@+id/et_store_rating"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@android:color/transparent"
                                android:textColor="@android:color/darker_gray"
                                android:textSize="12sp"
                                tools:text="88VIP好评率99%, 平均3天内发货" />
                        </LinearLayout>

                        <Button
                            android:id="@+id/btn_visit_store"
                            style="?android:attr/borderlessButtonStyle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="进店逛逛 >"
                            android:textColor="@android:color/darker_gray"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <!-- Divider -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#F0F0F0"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="12dp"/>

                    <!-- Product Section -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/iv_product_image_detail"
                            android:layout_width="100dp"
                            android:layout_height="100dp"
                            android:scaleType="centerCrop"
                            android:src="@drawable/test" />

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp">

                            <EditText
                                android:id="@+id/et_product_price_detail"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentEnd="true"
                                android:layout_marginTop="6dp"
                                android:background="@android:color/transparent"
                                android:textColor="@android:color/black"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                tools:text="¥50" />

                            <EditText
                                android:id="@+id/et_product_name_detail"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="6dp"
                                android:layout_toStartOf="@id/et_product_price_detail"
                                android:background="@android:color/transparent"
                                android:ellipsize="end"
                                android:maxLines="1"
                                android:singleLine="true"
                                android:textColor="@android:color/black"
                                android:textSize="15sp"
                                android:textStyle="bold"
                                tools:text="【原神官方/定金】芙宁娜·甜香" />

                            <TextView
                                android:id="@+id/tv_product_desc_detail"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_below="@id/et_product_name_detail"
                                android:layout_marginTop="10dp"
                                android:textColor="@android:color/darker_gray"
                                android:textSize="14sp"
                                tools:text="海军蓝，256G，6G内存" />

                            <LinearLayout
                                android:id="@+id/tags_container_detail"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_below="@id/tv_product_desc_detail"
                                android:layout_marginTop="14dp"
                                android:orientation="horizontal" />

                            <EditText
                                android:id="@+id/et_quantity_detail"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_below="@id/et_product_price_detail"
                                android:layout_alignParentEnd="true"
                                android:layout_marginTop="10dp"
                                android:background="@android:color/transparent"
                                android:textColor="@android:color/darker_gray"
                                android:textSize="14sp"
                                tools:text="x1" />

                        </RelativeLayout>

                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Order Info Section in CardView -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                app:cardCornerRadius="0dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:drawableEnd="@drawable/ic_arrow_down_gray"
                            android:drawablePadding="4dp"
                            android:text="实付款"
                            android:textSize="15sp"
                            android:textColor="@color/black_text"/>

                        <TextView
                            android:id="@+id/tv_total_payment"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:textSize="18sp"
                            android:text="¥50"
                            android:textColor="@android:color/black"
                            android:textStyle="bold" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:text="订单编号"
                            android:textSize="15sp"
                            android:textColor="@color/black_text"/>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <EditText
                                android:id="@+id/et_order_number"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:background="@android:color/transparent"
                                android:textColor="@android:color/darker_gray"
                                android:textSize="13sp"
                                tools:text="4610644706593328843" />

                            <Button
                                android:id="@+id/btn_copy_order_number"
                                style="?android:attr/borderlessButtonStyle"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:minWidth="0dp"
                                android:minHeight="0dp"
                                android:paddingStart="8dp"
                                android:paddingEnd="8dp"
                                android:text="复制"
                                android:textSize="12sp"
                                android:textColor="@color/black_text" />
                        </LinearLayout>
                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp">
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="交易快照"
                            android:textSize="15sp"
                            android:textColor="@color/black_text"/>
                        <EditText
                            android:id="@+id/et_transaction_snapshot"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:background="@android:color/transparent"
                            android:gravity="end"
                            android:textSize="13sp"
                            android:textColor="@android:color/darker_gray"
                            tools:text="发生交易争议时,可作为判断依据" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp">
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="天猫积分"
                            android:textSize="15sp"
                            android:textColor="@color/black_text"/>
                        <EditText
                            android:id="@+id/et_tmall_points"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:background="@android:color/transparent"
                            android:gravity="end"
                            android:textSize="13sp"
                            android:textColor="@android:color/darker_gray"
                            tools:text="获得25点积分" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:text="微信交易号"
                            android:textSize="15sp"
                            android:textColor="@color/black_text"/>

                        <EditText
                            android:id="@+id/et_wechat_transaction_id"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:background="@android:color/transparent"
                            android:gravity="end"
                            android:textSize="13sp"
                            android:textColor="@android:color/darker_gray"
                            tools:text="4349502572202507022480548733" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:text="创建时间"
                            android:textSize="15sp"
                            android:textColor="@color/black_text"/>

                        <EditText
                            android:id="@+id/et_creation_time"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:background="@android:color/transparent"
                            android:gravity="end"
                            android:textSize="13sp"
                            android:textColor="@android:color/darker_gray"
                            tools:text="2025-07-02 16:33:07" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:text="付款时间"
                            android:textSize="15sp"
                            android:textColor="@color/black_text"/>

                        <EditText
                            android:id="@+id/et_payment_time"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:background="@android:color/transparent"
                            android:gravity="end"
                            android:textSize="13sp"
                            android:textColor="@android:color/darker_gray"
                            tools:text="2025-07-02 16:33:14" />
                    </RelativeLayout>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="16dp"
                        android:drawableEnd="@drawable/ic_arrow_up_gray"
                        android:drawablePadding="4dp"
                        android:text="收起更多订单信息"
                        android:textColor="@android:color/darker_gray"
                        android:textSize="13sp" />
                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Order Services Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                app:cardCornerRadius="0dp"
                app:cardElevation="0dp">
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="16dp">
                    <ImageView
                        android:id="@+id/iv_services_icon"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/orderservice"
                        android:layout_centerVertical="true"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_toEndOf="@id/iv_services_icon"
                        android:layout_marginStart="8dp"
                        android:text="订单服务"
                        android:textColor="@android:color/black"
                        android:layout_centerVertical="true"/>

                    <EditText
                        android:id="@+id/et_order_services"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:background="@android:color/transparent"
                        android:gravity="end"
                        android:textSize="14sp"
                        android:textColor="@android:color/darker_gray"
                        tools:text="包含假一赔四等服务" />
                    <TextView
                        android:id="@+id/tv_order_services"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:drawableEnd="@drawable/ic_chevron_right_gray"
                        android:drawablePadding="4dp"
                        android:visibility="gone"
                        tools:text="包含假一赔四等服务" />
                </RelativeLayout>
            </androidx.cardview.widget.CardView>

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:src="@drawable/photo"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter" />

        </LinearLayout>
    </ScrollView>

    <LinearLayout
        android:id="@+id/bottom_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/white"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="8dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingStart="12dp"
            android:paddingEnd="12dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_customer_service_orange"
                android:tint="@null" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="客服"
                android:textSize="10sp"
                android:textColor="@android:color/darker_gray" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingStart="12dp"
            android:paddingEnd="12dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_complaint_orange"
                android:tint="@null" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="投诉"
                android:textSize="10sp"
                android:textColor="@android:color/darker_gray" />
        </LinearLayout>

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <Button
            android:id="@+id/btn_request_invoice"
            style="@style/TaobaoGrayButton"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:textSize="13sp"
            android:textColor="@android:color/black"
            android:paddingStart="18dp"
            android:paddingEnd="18dp"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:text="申请开票" />

        <Button
            android:id="@+id/btn_urge_shipment_detail"
            style="@style/OrangeButton"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:layout_marginStart="8dp"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:textSize="13sp"
            android:text="催发货" />

        <!-- <Button
            android:id="@+id/btn_modify_address_detail"
            style="@style/TaobaoOrangeButton"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:layout_marginStart="8dp"
            android:textSize="13sp"
            android:text="修改地址" /> -->

    </LinearLayout>

</RelativeLayout>