package com.example.taobao.ui;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\n\b\u0007\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001:\u0003\u001a\u001b\u001cB\u0013\u0012\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\u0002\u0010\u0006J\b\u0010\u000b\u001a\u00020\fH\u0016J\u0018\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00022\u0006\u0010\u0010\u001a\u00020\fH\u0016J\u0018\u0010\u0011\u001a\u00020\u00022\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\fH\u0016J\u000e\u0010\u0015\u001a\u00020\u000e2\u0006\u0010\u0016\u001a\u00020\bJ\u000e\u0010\u0017\u001a\u00020\u000e2\u0006\u0010\u0016\u001a\u00020\nJ\u0014\u0010\u0018\u001a\u00020\u000e2\f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\t\u001a\u0004\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001d"}, d2 = {"Lcom/example/taobao/ui/OrderPendingAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/example/taobao/ui/OrderPendingAdapter$ViewHolder;", "orders", "", "Lcom/example/taobao/ui/OrderPending;", "(Ljava/util/List;)V", "buttonClickListener", "Lcom/example/taobao/ui/OrderPendingAdapter$OnButtonClickListener;", "itemClickListener", "Lcom/example/taobao/ui/OrderPendingAdapter$OnItemClickListener;", "getItemCount", "", "onBindViewHolder", "", "holder", "position", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "setOnButtonClickListener", "listener", "setOnItemClickListener", "updateOrders", "newOrders", "OnButtonClickListener", "OnItemClickListener", "ViewHolder", "app_debug"})
public final class OrderPendingAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.example.taobao.ui.OrderPendingAdapter.ViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private java.util.List<com.example.taobao.ui.OrderPending> orders;
    @org.jetbrains.annotations.Nullable()
    private com.example.taobao.ui.OrderPendingAdapter.OnButtonClickListener buttonClickListener;
    @org.jetbrains.annotations.Nullable()
    private com.example.taobao.ui.OrderPendingAdapter.OnItemClickListener itemClickListener;
    
    public OrderPendingAdapter(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.taobao.ui.OrderPending> orders) {
        super();
    }
    
    public final void setOnButtonClickListener(@org.jetbrains.annotations.NotNull()
    com.example.taobao.ui.OrderPendingAdapter.OnButtonClickListener listener) {
    }
    
    public final void setOnItemClickListener(@org.jetbrains.annotations.NotNull()
    com.example.taobao.ui.OrderPendingAdapter.OnItemClickListener listener) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.example.taobao.ui.OrderPendingAdapter.ViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.example.taobao.ui.OrderPendingAdapter.ViewHolder holder, int position) {
    }
    
    public final void updateOrders(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.taobao.ui.OrderPending> newOrders) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\u0006\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\u0007\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\b\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\t\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\n\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&\u00a8\u0006\u000b"}, d2 = {"Lcom/example/taobao/ui/OrderPendingAdapter$OnButtonClickListener;", "", "onApplyForInvoiceClick", "", "order", "Lcom/example/taobao/ui/OrderPending;", "onCheckLogisticsClick", "onConfirmReceiptClick", "onModifyAddressClick", "onMoreClick", "onUrgeShipmentClick", "app_debug"})
    public static abstract interface OnButtonClickListener {
        
        public abstract void onModifyAddressClick(@org.jetbrains.annotations.NotNull()
        com.example.taobao.ui.OrderPending order);
        
        public abstract void onUrgeShipmentClick(@org.jetbrains.annotations.NotNull()
        com.example.taobao.ui.OrderPending order);
        
        public abstract void onApplyForInvoiceClick(@org.jetbrains.annotations.NotNull()
        com.example.taobao.ui.OrderPending order);
        
        public abstract void onConfirmReceiptClick(@org.jetbrains.annotations.NotNull()
        com.example.taobao.ui.OrderPending order);
        
        public abstract void onCheckLogisticsClick(@org.jetbrains.annotations.NotNull()
        com.example.taobao.ui.OrderPending order);
        
        public abstract void onMoreClick(@org.jetbrains.annotations.NotNull()
        com.example.taobao.ui.OrderPending order);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0016\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\bf\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&\u00a8\u0006\u0006"}, d2 = {"Lcom/example/taobao/ui/OrderPendingAdapter$OnItemClickListener;", "", "onItemClick", "", "order", "Lcom/example/taobao/ui/OrderPending;", "app_debug"})
    public static abstract interface OnItemClickListener {
        
        public abstract void onItemClick(@org.jetbrains.annotations.NotNull()
        com.example.taobao.ui.OrderPending order);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0013\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\t\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\bR\u0011\u0010\u000b\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\bR\u0011\u0010\r\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0015\u001a\u00020\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u0019\u001a\u00020\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0018R\u0011\u0010\u001b\u001a\u00020\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0018R\u0011\u0010\u001d\u001a\u00020\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0018R\u0011\u0010\u001f\u001a\u00020\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0018R\u0011\u0010!\u001a\u00020\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u0018R\u0011\u0010#\u001a\u00020\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\u0018R\u0011\u0010%\u001a\u00020\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\u0018R\u0011\u0010\'\u001a\u00020\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010\u0018\u00a8\u0006)"}, d2 = {"Lcom/example/taobao/ui/OrderPendingAdapter$ViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "view", "Landroid/view/View;", "(Landroid/view/View;)V", "btnApplyForInvoice", "Landroid/widget/Button;", "getBtnApplyForInvoice", "()Landroid/widget/Button;", "btnModifyAddress", "getBtnModifyAddress", "btnUrgeShipment", "getBtnUrgeShipment", "ivProduct", "Landroid/widget/ImageView;", "getIvProduct", "()Landroid/widget/ImageView;", "tagsContainer", "Landroid/widget/LinearLayout;", "getTagsContainer", "()Landroid/widget/LinearLayout;", "tvOrderStatus", "Landroid/widget/TextView;", "getTvOrderStatus", "()Landroid/widget/TextView;", "tvPayAmount", "getTvPayAmount", "tvProductDesc", "getTvProductDesc", "tvProductName", "getTvProductName", "tvProductPrice", "getTvProductPrice", "tvQuantity", "getTvQuantity", "tvShippingPromise", "getTvShippingPromise", "tvShippingStatus", "getTvShippingStatus", "tvShopName", "getTvShopName", "app_debug"})
    public static final class ViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvShopName = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvOrderStatus = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView ivProduct = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvProductName = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvProductDesc = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvQuantity = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvProductPrice = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvShippingStatus = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvShippingPromise = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView tvPayAmount = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.Button btnModifyAddress = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.Button btnUrgeShipment = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.Button btnApplyForInvoice = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.LinearLayout tagsContainer = null;
        
        public ViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View view) {
            super(null);
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTvShopName() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTvOrderStatus() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.ImageView getIvProduct() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTvProductName() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTvProductDesc() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTvQuantity() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTvProductPrice() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTvShippingStatus() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTvShippingPromise() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.TextView getTvPayAmount() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.Button getBtnModifyAddress() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.Button getBtnUrgeShipment() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.Button getBtnApplyForInvoice() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final android.widget.LinearLayout getTagsContainer() {
            return null;
        }
    }
}