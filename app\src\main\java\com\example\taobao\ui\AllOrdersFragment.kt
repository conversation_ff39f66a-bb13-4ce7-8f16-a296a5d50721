package com.example.taobao.ui

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.taobao.R
import android.widget.Toast
import com.example.taobao.MainActivity
import com.example.taobao.ui.AddOrderFragment
import com.example.taobao.ui.EditOrderFragment
import com.example.taobao.ui.PendingReceiptFragment
import com.example.taobao.ui.PendingShipmentFragment
import com.example.taobao.ui.PlaceholderFragment

class AllOrdersFragment : Fragment() {

    private val viewModel: OrderViewModel by activityViewModels()
    private lateinit var filterButtons: List<View>

    // Fragment instances for tab navigation
    private val placeholderFragment = PlaceholderFragment.newInstance("功能待实现")
    private var addOrderFragment: AddOrderFragment? = null
    private val editOrderFragment = EditOrderFragment()

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_all_orders, container, false)

        setupFilterButtons(view)
        
        // Initial fragment load with all orders and highlight the "All" button
        if (savedInstanceState == null) {
            childFragmentManager.beginTransaction()
                .replace(R.id.order_list_container, PendingReceiptFragment.newInstance(false))
                .commit()
            updateButtonStyles(view.findViewById(R.id.filter_all))
        }
        
        setupFilterClicks(view)
        setupNavigation()

        return view
    }

    private fun setupFilterButtons(view: View) {
        filterButtons = listOf(
            view.findViewById(R.id.filter_all),
            view.findViewById(R.id.filter_pending_payment),
            view.findViewById(R.id.filter_pending_shipment),
            view.findViewById(R.id.filter_pending_receipt),
            view.findViewById(R.id.filter_refund),
            view.findViewById(R.id.filter_review),
            view.findViewById(R.id.filter_add_order),
            view.findViewById(R.id.filter_edit_order)
        )
    }
    
    private fun setupFilterClicks(view: View) {
        view.findViewById<View>(R.id.filter_all).setOnClickListener {
            updateButtonStyles(it)
            switchFragment(PendingReceiptFragment.newInstance(false))
        }
        view.findViewById<View>(R.id.filter_pending_payment).setOnClickListener {
            updateButtonStyles(it)
            switchFragment(placeholderFragment)
        }
        view.findViewById<View>(R.id.filter_pending_shipment).setOnClickListener {
            updateButtonStyles(it)
            switchFragment(PendingShipmentFragment.newInstance(true))
        }
        view.findViewById<View>(R.id.filter_pending_receipt).setOnClickListener {
            updateButtonStyles(it)
            switchFragment(PendingReceiptFragment.newInstance(true))
        }
        view.findViewById<View>(R.id.filter_refund).setOnClickListener {
            updateButtonStyles(it)
            switchFragment(placeholderFragment)
        }
        view.findViewById<View>(R.id.filter_review).setOnClickListener {
            updateButtonStyles(it)
            switchFragment(placeholderFragment)
            }
        view.findViewById<View>(R.id.filter_add_order).setOnClickListener {
            updateButtonStyles(it)
            switchFragment(getOrCreateAddOrderFragment())
        }
        view.findViewById<View>(R.id.filter_edit_order).setOnClickListener {
            updateButtonStyles(it)
            switchFragment(editOrderFragment)
        }
    }
    
    private fun setupNavigation() {
        viewModel.navigateToEditOrder.observe(viewLifecycleOwner) { navigate ->
            if (navigate == true) {
                // Find the "Add Order" button and update its style to selected
                val addOrderButton = view?.findViewById<View>(R.id.filter_add_order)
                addOrderButton?.let { updateButtonStyles(it) }

                switchFragment(getOrCreateAddOrderFragment())
                // Don't call onDoneNavigating() immediately - let AddOrderFragment handle the data first
                // viewModel.onDoneNavigating()
            }
        }
    }

    private fun getOrCreateAddOrderFragment(): AddOrderFragment {
        // Always create a new instance to avoid ActivityResultLauncher registration issues
        addOrderFragment = AddOrderFragment()
        return addOrderFragment!!
    }

    private fun updateButtonStyles(selectedButton: View) {
        for (button in filterButtons) {
            if (button is TextView) {
                if (button == selectedButton) {
                    button.setBackgroundResource(R.drawable.btn_taobao_orange_bg)
                    button.setTextColor(ContextCompat.getColor(requireContext(), R.color.orange_text))
                } else {
                    button.setBackgroundResource(R.drawable.btn_taobao_gray_bg)
                    button.setTextColor(ContextCompat.getColor(requireContext(), R.color.black_text))
                }
            }
        }
    }
    
    private fun switchFragment(fragment: Fragment) {
        childFragmentManager.beginTransaction()
            .replace(R.id.order_list_container, fragment)
            .commit()
    }
} 