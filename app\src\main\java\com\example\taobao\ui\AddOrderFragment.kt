package com.example.taobao.ui

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.example.taobao.R
import com.example.taobao.databinding.FragmentAddOrderBinding
import java.util.*
import com.example.taobao.ui.OrderPending

class AddOrderFragment : Fragment() {

    private var _binding: FragmentAddOrderBinding? = null
    private val binding get() = _binding!!
    private lateinit var viewModel: OrderViewModel
    private var imageUri: Uri? = null
    private var shopIconUri: Uri? = null
    private var editingOrder: OrderPending? = null

    // ActivityResultLauncher for picking a product image
    private val pickImageLauncher = registerForActivityResult(ActivityResultContracts.GetContent()) { uri: Uri? ->
        uri?.let {
            // Take persistent URI permission to avoid crashes after app restart
            try {
                requireContext().contentResolver.takePersistableUriPermission(
                    it,
                    Intent.FLAG_GRANT_READ_URI_PERMISSION
                )
            } catch (e: SecurityException) {
                // Handle the case where persistent permission is not available
                // This might happen with some content providers
            }
            imageUri = it
            binding.ivProductImage.setImageURI(it)
        }
    }

    // ActivityResultLauncher for picking a shop icon
    private val pickShopIconLauncher = registerForActivityResult(ActivityResultContracts.GetContent()) { uri: Uri? ->
        uri?.let {
            // Take persistent URI permission to avoid crashes after app restart
            try {
                requireContext().contentResolver.takePersistableUriPermission(
                    it,
                    Intent.FLAG_GRANT_READ_URI_PERMISSION
                )
            } catch (e: SecurityException) {
                // Handle the case where persistent permission is not available
                // This might happen with some content providers
            }
            shopIconUri = it
            binding.ivShopIcon.setImageURI(it)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        _binding = FragmentAddOrderBinding.inflate(inflater, container, false)
        viewModel = ViewModelProvider(requireActivity()).get(OrderViewModel::class.java)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        binding.btnUploadImage.setOnClickListener {
            pickImageLauncher.launch("image/*")
        }

        binding.btnUploadShopIcon.setOnClickListener {
            pickShopIconLauncher.launch("image/*")
        }
        
        // 设置RadioButton的监听器，根据选择自动填充订单状态
        binding.rgOrderType.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.rb_pending_shipment -> {
                    binding.etOrderStatus.setText("等待卖家发货")
                    binding.tvAddOrderTitle.text = if (editingOrder != null) "编辑待发货订单" else "添加待发货订单"
                }
                R.id.rb_pending_receipt -> {
                    binding.etOrderStatus.setText("卖家已发货")
                    binding.tvAddOrderTitle.text = if (editingOrder != null) "编辑待收货订单" else "添加待收货订单"
                }
            }
        }
        
        // 初始化订单状态
        if (binding.rbPendingShipment.isChecked) {
            binding.etOrderStatus.setText("等待卖家发货")
            binding.tvAddOrderTitle.text = "添加待发货订单"
        }

        binding.btnSaveOrder.setOnClickListener {
            saveOrder()
        }

        viewModel.selectedOrderForEdit.observe(viewLifecycleOwner) { order ->
            order?.let {
                editingOrder = it
                populateForm(it)
                // Clear only the navigation flags, keep the selected order data
                viewModel.clearNavigationFlags()
            }
        }
    }

    private fun formatPrice(priceInput: String): String {
        val cleanPrice = priceInput.trim()
        return if (cleanPrice.isNotEmpty() && !cleanPrice.startsWith("¥")) {
            "¥$cleanPrice"
        } else {
            cleanPrice
        }
    }

    override fun onResume() {
        super.onResume()
        // Check if there's an order to edit when fragment becomes visible
        viewModel.selectedOrderForEdit.value?.let { order ->
            if (editingOrder == null) {
                editingOrder = order
                populateForm(order)
            }
        }
    }

    private fun populateForm(order: OrderPending) {
        binding.etShopName.setText(order.shopName)
        binding.etOrderStatus.setText(order.orderStatus)
        binding.etProductName.setText(order.productName)
        binding.etProductPrice.setText(order.productPrice.replace("¥", "").replace(",", ""))
        binding.etProductDesc.setText(order.productDesc)
        binding.etQuantity.setText(order.quantity)
        binding.etShippingStatus.setText(order.shippingStatus)
        binding.etShippingPromise.setText(order.shippingPromise)
        binding.etTags.setText(order.tags.joinToString(","))
        binding.etLogistics.setText(order.logistics)
        binding.etPayInfo.setText(order.payInfo.replace("¥", "").replace(",", ""))
        if (!order.productImgUri.isNullOrEmpty()) {
            try {
                imageUri = Uri.parse(order.productImgUri)
                binding.ivProductImage.setImageURI(imageUri)
            } catch (e: Exception) {
                // If URI is invalid, fall back to default image
                binding.ivProductImage.setImageResource(order.productImg)
                imageUri = null
            }
        } else {
            binding.ivProductImage.setImageResource(order.productImg)
        }
        order.shopIconUri?.let {
            try {
                val uri = Uri.parse(it)
                binding.ivShopIcon.setImageURI(uri)
                shopIconUri = uri
            } catch (e: Exception) {
                // If URI is invalid, fall back to default icon
                binding.ivShopIcon.setImageResource(R.drawable.ic_add_photo)
                shopIconUri = null
            }
        }
        
        // 根据订单状态设置RadioButton
        if (order.orderStatus == "等待卖家发货") {
            binding.rbPendingShipment.isChecked = true
            binding.tvAddOrderTitle.text = "编辑待发货订单"
        } else if (order.orderStatus == "卖家已发货") {
            binding.rbPendingReceipt.isChecked = true
            binding.tvAddOrderTitle.text = "编辑待收货订单"
        }
        // Populate new fields
        binding.etBuyerAddress.setText(order.buyerAddress)
        binding.etBuyerName.setText(order.buyerName)
        binding.etOrderStatusText.setText(order.orderStatusText)
        binding.etStoreRating.setText(order.storeRating)
        binding.etOrderNumber.setText(order.orderNumber)
        binding.etTransactionSnapshot.setText(order.transactionSnapshotText)
        binding.etTmallPoints.setText(order.tmallPoints)
        binding.etWechatTransactionId.setText(order.wechatTransactionId)
        binding.etCreationTime.setText(order.creationTime)
        binding.etPaymentTime.setText(order.paymentTime)
        binding.etOrderServices.setText(order.orderServices)
        binding.etPresaleInfo.setText(order.presaleInfo)
    }

    private fun saveOrder() {
        // 根据选择的订单类型设置默认的订单状态和物流状态
        val orderStatus = if (binding.rbPendingShipment.isChecked) {
            "等待卖家发货"
        } else {
            "卖家已发货"
        }
        
        val shippingStatus = if (binding.etShippingStatus.text.toString().isEmpty()) {
            if (binding.rbPendingShipment.isChecked) {
                "待发货"
            } else {
                "运输中"
            }
        } else {
            binding.etShippingStatus.text.toString()
        }
        
        val orderToSave = OrderPending(
            id = editingOrder?.id ?: UUID.randomUUID().toString(),
            shopName = binding.etShopName.text.toString(),
            shopIconUri = shopIconUri?.toString(),
            orderStatus = orderStatus,
            productImg = R.drawable.test,
            productImgUri = imageUri?.toString(),
            productName = binding.etProductName.text.toString(),
            productPrice = formatPrice(binding.etProductPrice.text.toString()),
            productDesc = binding.etProductDesc.text.toString(),
            quantity = binding.etQuantity.text.toString(),
            shippingStatus = shippingStatus,
            shippingPromise = binding.etShippingPromise.text.toString(),
            tags = binding.etTags.text.toString().split(",").map { it.trim() },
            logistics = binding.etLogistics.text.toString(),
            payInfo = formatPrice(binding.etPayInfo.text.toString()),
            // Save new fields
            buyerAddress = binding.etBuyerAddress.text.toString(),
            buyerName = binding.etBuyerName.text.toString(),
            orderStatusText = binding.etOrderStatusText.text.toString(),
            storeRating = binding.etStoreRating.text.toString(),
            orderNumber = binding.etOrderNumber.text.toString(),
            transactionSnapshotText = binding.etTransactionSnapshot.text.toString(),
            tmallPoints = binding.etTmallPoints.text.toString(),
            wechatTransactionId = binding.etWechatTransactionId.text.toString(),
            creationTime = binding.etCreationTime.text.toString(),
            paymentTime = binding.etPaymentTime.text.toString(),
            orderServices = binding.etOrderServices.text.toString(),
            presaleInfo = binding.etPresaleInfo.text.toString()
        )
        
        if (editingOrder != null) {
            viewModel.updateOrder(orderToSave)
            Toast.makeText(context, "订单已更新", Toast.LENGTH_SHORT).show()
            // Clear editing state after successful update
            viewModel.clearEditingOrder()
            editingOrder = null
        } else {
            viewModel.addOrder(orderToSave)
            Toast.makeText(context, "订单已保存", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        // Don't clear editingOrder here as it might be needed when view is recreated
        // editingOrder = null
        shopIconUri = null
    }
}
