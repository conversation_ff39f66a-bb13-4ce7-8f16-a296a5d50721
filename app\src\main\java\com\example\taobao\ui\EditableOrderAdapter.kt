package com.example.taobao.ui

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.taobao.databinding.ItemOrderEditableBinding
import com.example.taobao.ui.OrderPending

class EditableOrderAdapter(
    private val onDeleteClick: (OrderPending) -> Unit,
    private val onEditClick: (OrderPending) -> Unit
) : ListAdapter<OrderPending, EditableOrderAdapter.OrderViewHolder>(OrderDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrderViewHolder {
        val binding = ItemOrderEditableBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return OrderViewHolder(binding)
    }

    override fun onBindViewHolder(holder: OrderViewHolder, position: Int) {
        val order = getItem(position)
        holder.bind(order, onDeleteClick, onEditClick)
    }

    class OrderViewHolder(private val binding: ItemOrderEditableBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(order: OrderPending, onDeleteClick: (OrderPending) -> Unit, onEditClick: (OrderPending) -> Unit) {
            binding.tvProductName.text = order.productName
            binding.tvShopName.text = order.shopName
            binding.tvOrderStatus.text = order.orderStatus
            binding.btnDelete.setOnClickListener { onDeleteClick(order) }
            binding.btnEdit.setOnClickListener { onEditClick(order) }
        }
    }

    class OrderDiffCallback : DiffUtil.ItemCallback<OrderPending>() {
        override fun areItemsTheSame(oldItem: OrderPending, newItem: OrderPending): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: OrderPending, newItem: OrderPending): Boolean {
            return oldItem == newItem
        }
    }
} 