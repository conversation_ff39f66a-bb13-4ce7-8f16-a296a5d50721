<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:background="@color/white"
    android:padding="12dp"
    android:layout_marginBottom="8dp">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tv_product_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Apple/苹果 iPhone 12 Pro ..."
            android:textColor="@color/black_text"
            android:textSize="15sp"/>
        <TextView
            android:id="@+id/tv_shop_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Apple Store 官方旗舰店"
            android:textColor="@color/gray_text"
            android:textSize="13sp"
            android:layout_marginTop="4dp"/>
            
        <TextView
            android:id="@+id/tv_order_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="卖家已发货"
            android:textColor="@color/taobao_orange"
            android:textSize="13sp"
            android:layout_marginTop="4dp"/>
    </LinearLayout>

    <Button
        android:id="@+id/btn_edit"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:text="编辑"
        style="@style/GrayButton"
        android:layout_marginStart="8dp"/>

    <Button
        android:id="@+id/btn_delete"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:text="删除"
        style="@style/RedBorderButton"
        android:layout_marginStart="8dp"/>

</LinearLayout> 