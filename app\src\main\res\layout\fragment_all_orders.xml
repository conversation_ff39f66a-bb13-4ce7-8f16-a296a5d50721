<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/taobao_bg">

    <!-- Header containing Top Nav and Filters -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/white"
        android:paddingBottom="10dp">

        <!-- Top Navigation -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="16dp"
            android:paddingEnd="16dp">

            <TextView
                android:id="@+id/nav_all_orders"
                android:layout_width="wrap_content"
            android:layout_height="wrap_content"
                android:text="全部订单"
                android:textColor="@color/taobao_orange"
                android:textSize="16sp"
                android:textStyle="bold"/>

            <View
                android:layout_width="30dp"
                android:layout_height="3dp"
                android:layout_below="@id/nav_all_orders"
                android:layout_alignStart="@id/nav_all_orders"
                android:layout_alignEnd="@id/nav_all_orders"
                android:layout_marginTop="4dp"
                android:background="@drawable/tab_indicator"/>

            <TextView
                android:id="@+id/nav_shopping"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toEndOf="@id/nav_all_orders"
                android:layout_alignBaseline="@id/nav_all_orders"
                android:text="购物"
                android:textColor="@color/black_text"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginStart="24dp"/>

            <TextView
                android:id="@+id/nav_flash_buy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_toEndOf="@id/nav_shopping"
                android:layout_alignBaseline="@id/nav_all_orders"
                android:text="闪购"
                android:textColor="@color/black_text"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginStart="24dp"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="外卖"
                android:layout_toEndOf="@id/nav_flash_buy"
                android:layout_alignBaseline="@id/nav_all_orders"
                android:textColor="@color/white"
                android:background="@drawable/waimai_tag_bg"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:paddingTop="1dp"
                android:paddingBottom="1dp"
                android:textSize="10sp"
                android:layout_marginStart="1dp"/>

        </RelativeLayout>

        <!-- Filter Buttons -->
        <HorizontalScrollView
                android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none"
            android:layout_marginTop="16dp"
            android:paddingStart="16dp"
            android:paddingEnd="16dp">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView style="@style/TaobaoFilterButton" android:text="全部" android:id="@+id/filter_all"/>
                <TextView style="@style/TaobaoFilterButton" android:text="待付款" android:id="@+id/filter_pending_payment" android:layout_marginStart="8dp"/>
                <TextView style="@style/TaobaoFilterButton" android:text="待发货" android:id="@+id/filter_pending_shipment" android:layout_marginStart="8dp"/>
                <TextView style="@style/TaobaoFilterButton.Selected" android:text="待收货" android:id="@+id/filter_pending_receipt" android:layout_marginStart="8dp"/>
                <TextView style="@style/TaobaoFilterButton" android:text="退款/售后" android:id="@+id/filter_refund" android:layout_marginStart="8dp"/>
                <TextView style="@style/TaobaoFilterButton" android:text="待评价" android:id="@+id/filter_review" android:layout_marginStart="8dp"/>
                <TextView style="@style/TaobaoFilterButton" android:text="添加订单" android:id="@+id/filter_add_order" android:layout_marginStart="8dp"/>
                <TextView style="@style/TaobaoFilterButton" android:text="编辑订单" android:id="@+id/filter_edit_order" android:layout_marginStart="8dp"/>

        </LinearLayout>
        </HorizontalScrollView>
    </LinearLayout>

    <FrameLayout
        android:id="@+id/order_list_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</LinearLayout> 